"""
Embedding and clustering utilities for curriculum learning.

This module provides functions to extract embeddings from text data
and cluster them for use in curriculum learning.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional, Union
import logging
import time
from sklearn.cluster import KMeans
from sentence_transformers import SentenceTransformer
import seaborn as sns
import matplotlib.pyplot as plt
from umap import UMAP
import torch
import textstat
import re
import string

logger = logging.getLogger(__name__)

def extract_embeddings(
    texts: List[str],
    method: str = 'tfidf',
    model_name: Optional[str] = None,
    max_length: int = 512,
    batch_size: int = 32
) -> np.ndarray:
    """
    Extract embeddings from text data using sentence-transformers.

    Args:
        texts: List of text strings to embed
        model_name: Name of the model to use for embeddings
        max_length: Maximum sequence length for model embeddings
        batch_size: Batch size for model inference

    Returns:
        Array of embeddings
    """
    if model_name is None:
        model_name = 'sentence-transformers/all-MiniLM-L6-v2'  # Default model
        logger.info(f"No model specified, using default: {model_name}")

    logger.info(f"Using sentence-transformers model: {model_name}")
    start_time = time.time()

    # Process texts to handle different formats
    processed_texts = []
    for text in texts:
        if isinstance(text, np.ndarray) and len(text) > 0 and isinstance(text[0], dict) and 'content' in text[0]:
            # Handle array with dictionary containing 'content'
            processed_texts.append(text[0]['content'])
        else:
            # Convert to string for other formats
            processed_texts.append(str(text))

    # Load model and generate embeddings
    model = SentenceTransformer(model_name)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)  # Force CUDA usage
    embeddings = model.encode(processed_texts,
                            batch_size=batch_size,
                            show_progress_bar=True,
                            device=device
                            )

    end_time = time.time()
    logger.info(f"Embeddings generated. Shape: {embeddings.shape}")
    logger.info(f"Time taken for embedding: {end_time - start_time:.2f} seconds")

    return embeddings

def cluster_embeddings(
    embeddings: np.ndarray,
    num_clusters: int = 10,
    method: str = 'kmeans',
    random_state: int = 42,
    return_model: bool = False,
    auto_adjust_clusters: bool = False,
    target_std_ratio: float = 0.1  # 目标标准差与平均值的比率
) -> Union[np.ndarray, Tuple[np.ndarray, KMeans]]:
    """
    Cluster embeddings into groups.

    Args:
        embeddings: Array of embeddings to cluster
        num_clusters: Number of clusters to create (ignored if auto_adjust_clusters=True)
        method: Clustering method ('kmeans')
        random_state: Random seed for reproducibility
        return_model: Whether to return the KMeans model along with cluster IDs
        auto_adjust_clusters: Whether to automatically adjust number of clusters
        target_std_ratio: Target ratio of std/mean for cluster sizes (lower means more balanced)

    Returns:
        If return_model is False: Array of cluster assignments
        If return_model is True: Tuple of (cluster assignments, KMeans model)
    """
    n_samples = embeddings.shape[0]

    if not auto_adjust_clusters:
        # 原有的固定聚类数量逻辑
        if n_samples < num_clusters:
            logger.warning(f"Number of data points ({n_samples}) is less than requested clusters ({num_clusters}).")
            logger.warning(f"Reducing number of clusters to {n_samples}.")
            num_clusters = n_samples
    else:
        logger.info("Auto-adjusting number of clusters...")

        def evaluate_clustering(n_clusters):
            kmeans = KMeans(n_clusters=n_clusters, random_state=random_state, n_init='auto')
            cluster_ids = kmeans.fit_predict(embeddings)
            counts = np.bincount(cluster_ids)
            std_ratio = np.std(counts) / np.mean(counts)
            return std_ratio, kmeans, cluster_ids

        # 二分查找最佳聚类数量
        left = 4  # 最少2个簇
        right = 25  # 最多samples/10或100个簇
        best_std_ratio = float('inf')
        best_kmeans = None
        best_cluster_ids = None
        best_n_clusters = None

        while left <= right:
            mid = (left + right) // 2
            std_ratio, kmeans, cluster_ids = evaluate_clustering(mid)

            if std_ratio < best_std_ratio:
                best_std_ratio = std_ratio
                best_kmeans = kmeans
                best_cluster_ids = cluster_ids
                best_n_clusters = mid

            if std_ratio <= target_std_ratio:
                # 已经足够平衡，尝试减少簇的数量
                right = mid - 1
            else:
                # 还不够平衡，尝试增加簇的数量
                left = mid + 1

        logger.info(f"Auto-adjusted to {best_n_clusters} clusters with size std/mean ratio: {best_std_ratio:.3f}")

        if return_model:
            return best_cluster_ids, best_kmeans
        return best_cluster_ids

    if method == 'kmeans':
        logger.info(f"Performing K-means clustering with {num_clusters} clusters...")
        start_time = time.time()

        # Use n_init='auto' for newer sklearn versions to avoid warnings
        try:
            kmeans = KMeans(n_clusters=num_clusters, random_state=random_state, n_init='auto')
        except TypeError:
            # Fall back to default for older sklearn versions
            kmeans = KMeans(n_clusters=num_clusters, random_state=random_state, n_init=10)

        cluster_ids = kmeans.fit_predict(embeddings)

        end_time = time.time()
        logger.info(f"Clustering complete. Time taken: {end_time - start_time:.2f} seconds")

        # Log cluster distribution
        unique_clusters, counts = np.unique(cluster_ids, return_counts=True)
        cluster_distribution = dict(zip(unique_clusters, counts))
        logger.info(f"Cluster distribution: {cluster_distribution}")

        if return_model:
            return cluster_ids, kmeans
        return cluster_ids
    else:
        raise ValueError(f"Unknown clustering method: {method}")

def extract_text_features(text: str) -> np.ndarray:
    """
    Extract various text features including:
    - Total word count
    - Sentence count
    - Average word length
    - Average sentence length
    - Character count
    - Symbol count
    - Readability scores

    Args:
        text: Input text string

    Returns:
        Array of text features
    """
    # Clean and prepare text
    text = str(text)  # Ensure text is string

    # Word count
    words = text.split()
    word_count = len(words)

    # Sentence count (using simple period-based splitting)
    sentences = re.split('[.!?]+', text)
    sentences = [s.strip() for s in sentences if s.strip()]
    sentence_count = len(sentences)

    # Average word length
    avg_word_length = np.mean([len(word) for word in words]) if words else 0

    # Average sentence length (in words)
    avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0

    # Character count (excluding spaces)
    char_count = len(text.replace(" ", ""))

    # Symbol count (punctuation and special characters)
    symbols = string.punctuation + "¢£¥€"  # Add any other symbols you want to count
    symbol_count = sum(1 for char in text if char in symbols)

    # Readability scores
    flesch_reading_ease = textstat.flesch_reading_ease(text)
    flesch_kincaid_grade = textstat.flesch_kincaid_grade(text)
    automated_readability_index = textstat.automated_readability_index(text)

    # Combine all features
    features = np.array([
        word_count,
        sentence_count,
        avg_word_length,
        avg_sentence_length,
        char_count,
        symbol_count,
        flesch_reading_ease,
        flesch_kincaid_grade,
        automated_readability_index
    ], dtype=np.float64)

    # Normalize features to [0,1] range to match embedding scale
    features = (features - np.min(features)) / (np.max(features) - np.min(features) + 1e-8)

    return features

def assign_embedding_clusters(
    base_name: str,
    dataframe: pd.DataFrame,
    prompt_key: str = 'prompt',
    num_clusters: int = 10,
    model_name: Optional[str] = None,
    bucket_column: str = 'data_source',
    batch_size: int = 32,
    text_features_weight: float = 0.5,  # New parameter for text features weight
    visualize: bool = True,
    embedding_dim: int = 8,
    auto_adjust_clusters: bool = False,
    target_std_ratio: float = 0.3,
    use_embedding: bool = False,
    has_solved_percentage: bool = False
) -> pd.DataFrame:
    """
    Assign cluster IDs to examples in a dataframe based on text embeddings and text features.

    Args:
        base_name: Base name for the bucket labels
        dataframe: Pandas DataFrame containing the examples
        prompt_key: Column name containing the prompts
        num_clusters: Number of clusters to create
        model_name: Name of the model to use for embeddings
        bucket_column: Column name to store the cluster assignments
        batch_size: Batch size for model inference
        text_features_weight: Weight for text features vs embeddings (0.5 means equal weight)
        visualize: Whether to create visualization plots
        embedding_dim: Target dimension for embedding reduction (default: 8)
        auto_adjust_clusters: Whether to automatically adjust number of clusters
        target_std_ratio: Target ratio of std/mean for cluster sizes (lower means more balanced)
        use_embedding: Whether to use embeddings in clustering (default: False)
                      If False, will use solved_percentage (if available) and text features
        has_solved_percentage: Whether the dataframe has a 'solved_percentage' column

    Returns:
        DataFrame with an additional column containing cluster assignments
    """
    # Extract prompts from the dataframe
    prompts = []
    for i in range(len(dataframe)):
        prompt = dataframe.iloc[i][prompt_key]
        prompts.append(prompt)

    logger.info(f"Extracted {len(prompts)} prompts from column '{prompt_key}'")

    # Check if solved_percentage column exists
    has_solved_percentage = has_solved_percentage and 'solved_percentage' in dataframe.columns

    # Extract text features
    logger.info("Extracting text features")
    text_features = np.vstack([extract_text_features(prompt) for prompt in prompts])

    # Initialize reduced_embeddings as None
    reduced_embeddings = None

    if use_embedding:
        # Extract embeddings only if we're using them
        logger.info(f"Extracting embeddings using sentence-transformers")
        embeddings = extract_embeddings(
            texts=prompts,
            model_name=model_name,
            batch_size=batch_size
        )

        # Ensure embeddings is float64
        embeddings = embeddings.astype(np.float64)

        # Use UMAP for dimension reduction
        logger.info(f"Reducing embedding dimension from {embeddings.shape[1]} to {embedding_dim}")
        umap_reducer = UMAP(n_components=embedding_dim, random_state=42)
        reduced_embeddings = umap_reducer.fit_transform(embeddings)
        reduced_embeddings = reduced_embeddings.astype(np.float64)

        # Normalize reduced embeddings
        reduced_embeddings = (reduced_embeddings - reduced_embeddings.min(axis=0)) / (
            reduced_embeddings.max(axis=0) - reduced_embeddings.min(axis=0) + 1e-8
        )

        # Calculate weights
        embedding_weight = 1.0 - text_features_weight

        # Combine features with weights
        combined_features = np.concatenate([
            reduced_embeddings * embedding_weight,
            text_features * text_features_weight
        ], axis=1).astype(np.float64)
    else:
        # If not using embeddings, check if we have solved_percentage
        if has_solved_percentage:
            # Calculate difficulty as 100 - solved_percentage
            logger.info("Using solved_percentage as difficulty measure for clustering")
            difficulties = 100 - dataframe['solved_percentage'].values

            # Normalize difficulties to [0,1] range
            # normalized_difficulties = difficulties / 100.0
            normalized_difficulties = difficulties

            # Use difficulties as the clustering features
            combined_features = np.column_stack([
                text_features,
                normalized_difficulties.reshape(-1, 1)
            ]).astype(np.float64)
        else:
            # If no solved_percentage, just use text features
            logger.warning("No solved_percentage column found, using only text features for clustering")
            combined_features = text_features.astype(np.float64)

    # Cluster combined features
    logger.info(f"Clustering combined features into {num_clusters} clusters using kmeans")

    cluster_ids, _ = cluster_embeddings(
        embeddings=combined_features,
        num_clusters=num_clusters,
        return_model=True,
        auto_adjust_clusters=auto_adjust_clusters,
        target_std_ratio=target_std_ratio
    )

    # 确保cluster_ids是整数类型
    cluster_ids = cluster_ids.astype(np.int32)

    # 创建可视化
    if visualize:
        try:
            # Get difficulty values for visualization
            if has_solved_percentage:
                # Use difficulty (100 - solved_percentage) for visualization
                difficulty_values = 100 - dataframe['solved_percentage'].values
                logger.info("Using difficulty (100 - solved_percentage) for visualization")
            else:
                # Fallback to text length if no solved_percentage
                difficulty_values = np.array([len(str(text).split()) for text in prompts])
                logger.info("No solved_percentage column found, using text length for visualization")

            # Create 2D embeddings for visualization
            if use_embedding and reduced_embeddings is not None:
                # If using embeddings, create 2D projection of embeddings
                umap_2d = UMAP(n_components=2, random_state=42)
                vis_embeddings = umap_2d.fit_transform(reduced_embeddings)
            else:
                # If not using embeddings, create 2D projection of combined features
                umap_2d = UMAP(n_components=2, random_state=42)
                vis_embeddings = umap_2d.fit_transform(combined_features)

            # Call visualization function
            _ = visualize_clusters_with_boundaries(
                embeddings=vis_embeddings.astype(np.float64),
                text_features=text_features.astype(np.float64),
                lengths=difficulty_values.astype(np.float64),  # Using difficulty instead of length
                cluster_ids=cluster_ids,
                title=f"Clustering Results for {base_name}",
                save_path=f"images/cluster_visualization_{base_name}.png",
                use_difficulty=has_solved_percentage  # Pass flag to indicate if using difficulty
            )
        except Exception as e:
            logger.warning(f"Failed to create visualization: {e}")
            logger.warning(f"Error details: {str(e)}")  # 添加更详细的错误信息

    # Create bucket names and add to dataframe
    bucket_names = [f"{base_name}_cluster_{i}" for i in range(max(cluster_ids) + 1)]
    df_with_clusters = dataframe.copy()
    df_with_clusters[bucket_column] = [bucket_names[cluster_id] for cluster_id in cluster_ids]

    # Log cluster distribution
    cluster_counts = df_with_clusters[bucket_column].value_counts().to_dict()
    logger.info(f"Cluster distribution: {cluster_counts}")

    return df_with_clusters

def visualize_clusters_with_boundaries(
    embeddings: np.ndarray,
    text_features: np.ndarray,
    lengths: np.ndarray,
    cluster_ids: np.ndarray,
    title: str = "Clustering Visualization",
    save_path: Optional[str] = None,
    use_difficulty: bool = False
): # [TODO]修改可视化方式
    """
    Visualize clustering results with both embeddings and text features.
    Creates two subplots: one for embeddings and one for text features.

    Args:
        embeddings: Embedding vectors
        text_features: Text feature vectors
        lengths: Text lengths or difficulty values for heatmap coloring
        cluster_ids: Cluster assignments
        title: Plot title
        save_path: Path to save the visualization
        use_difficulty: Whether to use difficulty (True) or text length (False) for coloring
    """
    # 确保所有输入数据类型正确
    embeddings = np.asarray(embeddings, dtype=np.float64)
    text_features = np.asarray(text_features, dtype=np.float64)
    lengths = np.asarray(lengths, dtype=np.float64)
    cluster_ids = np.asarray(cluster_ids, dtype=np.int32)

    # 创建2x1的子图布局
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

    # 设置颜色方案
    n_clusters = len(np.unique(cluster_ids))
    cluster_colors = plt.cm.tab20(np.linspace(0, 1, n_clusters))

    try:
        # 1. 左侧子图：Embedding空间可视化
        umap_model_emb = UMAP(n_components=2, random_state=42)
        reduced_embeddings = umap_model_emb.fit_transform(embeddings)
        reduced_embeddings = reduced_embeddings.astype(np.float64)

        # 计算网格
        x_min = reduced_embeddings[:, 0].min() - 1
        x_max = reduced_embeddings[:, 0].max() + 1
        y_min = reduced_embeddings[:, 1].min() - 1
        y_max = reduced_embeddings[:, 1].max() + 1

        xx_emb = np.linspace(x_min, x_max, 100)
        yy_emb = np.linspace(y_min, y_max, 100)
        xx_emb, yy_emb = np.meshgrid(xx_emb, yy_emb)

        # 训练KMeans
        kmeans_emb = KMeans(n_clusters=n_clusters, random_state=42)
        kmeans_emb.fit(reduced_embeddings)

        # 预测网格点
        grid_points = np.c_[xx_emb.ravel(), yy_emb.ravel()]
        Z_emb = kmeans_emb.predict(grid_points)
        Z_emb = Z_emb.reshape(xx_emb.shape)

        # 绘制embedding空间
        ax1.contourf(xx_emb, yy_emb, Z_emb, alpha=0.2, cmap=plt.cm.tab20)

        # 2. 右侧子图：文本特征空间可视化
        umap_model_feat = UMAP(n_components=2, random_state=42)
        reduced_features = umap_model_feat.fit_transform(text_features)
        reduced_features = reduced_features.astype(np.float64)

        # 计算网格
        x_min = reduced_features[:, 0].min() - 1
        x_max = reduced_features[:, 0].max() + 1
        y_min = reduced_features[:, 1].min() - 1
        y_max = reduced_features[:, 1].max() + 1

        xx_feat = np.linspace(x_min, x_max, 100)
        yy_feat = np.linspace(y_min, y_max, 100)
        xx_feat, yy_feat = np.meshgrid(xx_feat, yy_feat)

        # 训练KMeans
        kmeans_feat = KMeans(n_clusters=n_clusters, random_state=42)
        kmeans_feat.fit(reduced_features)

        # 预测网格点
        grid_points = np.c_[xx_feat.ravel(), yy_feat.ravel()]
        Z_feat = kmeans_feat.predict(grid_points)
        Z_feat = Z_feat.reshape(xx_feat.shape)

        # 绘制特征空间
        ax2.contourf(xx_feat, yy_feat, Z_feat, alpha=0.2, cmap=plt.cm.tab20)

        # 绘制散点图
        scatter_emb = None
        scatter_feat = None

        for cluster_id in range(n_clusters):
            mask = cluster_ids == cluster_id

            # Embedding空间散点图
            scatter_emb = ax1.scatter(
                reduced_embeddings[mask, 0],
                reduced_embeddings[mask, 1],
                c=lengths[mask],
                cmap='YlOrRd',
                s=100,
                alpha=0.6,
                marker='o',
                edgecolors=cluster_colors[cluster_id],
                linewidth=1,
                label=f'Cluster {cluster_id}'
            )

            # 特征空间散点图
            scatter_feat = ax2.scatter(
                reduced_features[mask, 0],
                reduced_features[mask, 1],
                c=lengths[mask],
                cmap='YlOrRd',
                s=100,
                alpha=0.6,
                marker='o',
                edgecolors=cluster_colors[cluster_id],
                linewidth=1,
                label=f'Cluster {cluster_id}'
            )

        # 设置标题和标签
        ax1.set_title("Embedding Space Clustering", fontsize=12)
        ax1.set_xlabel("UMAP Dimension 1")
        ax1.set_ylabel("UMAP Dimension 2")

        ax2.set_title("Text Features Space Clustering", fontsize=12)
        ax2.set_xlabel("UMAP Dimension 1")
        ax2.set_ylabel("UMAP Dimension 2")

        # 添加颜色条
        colorbar_label = 'Difficulty' if use_difficulty else 'Text Length'
        if scatter_emb is not None:
            plt.colorbar(scatter_emb, ax=ax1, label=colorbar_label)
        if scatter_feat is not None:
            plt.colorbar(scatter_feat, ax=ax2, label=colorbar_label)

        # 添加图例
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # 设置总标题
        fig.suptitle(title, fontsize=14, y=1.05)
        plt.tight_layout()

        # 保存图像
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Visualization saved to {save_path}")

    except Exception as e:
        logger.error(f"Error in visualization: {str(e)}")
        plt.close()
        return None

    plt.close()
    return None

if __name__ == '__main__':
    import pandas as pd

    # Read parquet file
    file_path = "~/data/gsm8k_d/train_harder.parquet"
    # file_path = "/data/yzr/DUMP/combined_logic_dataset/generate_combined_kk/combined_logic_datasets_train.parquet"
    df = pd.read_parquet(file_path)

    # Assign clusters
    df = assign_embedding_clusters(
        base_name='gsm8k_d',
        dataframe=df,
        prompt_key='problem',
        num_clusters=10,
        bucket_column='cluster',
        embedding_dim=8,
        auto_adjust_clusters=False,
        use_embedding=False  # Set to False to use solved_percentage if available
    )

    # Print cluster distribution
    print(df['cluster'].value_counts())

    # Save results
    # df.to_parquet('train_with_clusters.parquet')
