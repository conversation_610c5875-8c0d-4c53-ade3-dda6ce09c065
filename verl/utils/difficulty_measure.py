# Copyright 2024 Bytedance Ltd. and/or its affiliates
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Difficulty measurement utilities for curriculum learning.

This module provides functions to measure the difficulty of examples
based on various metrics such as length, complexity, etc.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional, Union
import logging

logger = logging.getLogger(__name__)

def create_length_buckets(
    base_name: str,
    lengths: List[int],
    num_buckets: int = 5,
    method: str = 'quantile'
) -> Tuple[List[str], Dict[int, str]]:
    """
    Create buckets based on sequence lengths.

    Args:
        lengths: List of sequence lengths
        num_buckets: Number of buckets to create
        method: Method to create buckets ('quantile', 'equal_width', or 'log_scale')

    Returns:
        Tuple containing:
        - List of bucket names
        - Dictionary mapping indices to bucket names
    """
    if not lengths:
        return [], {}

    # Convert to numpy array for easier manipulation
    lengths_array = np.array(lengths)

    # Create bucket boundaries based on the specified method
    if method == 'quantile':
        # Create buckets with equal number of examples
        quantiles = np.linspace(0, 100, num_buckets + 1)
        boundaries = np.percentile(lengths_array, quantiles)
    elif method == 'equal_width':
        # Create buckets with equal width
        min_length = np.min(lengths_array)
        max_length = np.max(lengths_array)
        boundaries = np.linspace(min_length, max_length, num_buckets + 1)
    elif method == 'log_scale':
        # Create buckets with logarithmic scale (useful for highly skewed distributions)
        min_length = np.log1p(np.min(lengths_array))
        max_length = np.log1p(np.max(lengths_array))
        log_boundaries = np.linspace(min_length, max_length, num_buckets + 1)
        boundaries = np.expm1(log_boundaries)
    else:
        raise ValueError(f"Unknown bucketing method: {method}")

    # Ensure boundaries are unique and sorted
    boundaries = np.unique(boundaries)

    # Create bucket names
    bucket_names = []
    for i in range(len(boundaries) - 1):
        lower = int(boundaries[i])
        upper = int(boundaries[i + 1])
        if lower == upper:
            # Handle edge case where boundaries are the same
            bucket_name = f"{base_name}_length_{lower}"
        else:
            bucket_name = f"{base_name}_length_{lower}_to_{upper}"
        bucket_names.append(bucket_name)

    # Assign each example to a bucket
    index_to_bucket = {}
    for i, length in enumerate(lengths):
        # Find the bucket for this length
        bucket_idx = np.digitize(length, boundaries) - 1
        # Handle edge case where length is exactly equal to the maximum boundary
        if bucket_idx >= len(bucket_names):
            bucket_idx = len(bucket_names) - 1
        index_to_bucket[i] = bucket_names[bucket_idx]

    return bucket_names, index_to_bucket

def assign_length_buckets(
    base_name: str,
    dataframe: pd.DataFrame,
    tokenizer,
    prompt_key: str = 'prompt',
    num_buckets: int = 5,
    method: str = 'quantile',
    bucket_column: str = 'length_bucket'
) -> pd.DataFrame:
    """
    Assign length buckets to examples in a dataframe.

    Args:
        dataframe: Pandas DataFrame containing the examples
        tokenizer: Tokenizer to use for measuring length
        prompt_key: Column name containing the prompts
        num_buckets: Number of buckets to create
        method: Method to create buckets ('quantile', 'equal_width', or 'log_scale')
        bucket_column: Column name to store the bucket assignments

    Returns:
        DataFrame with an additional column containing bucket assignments
    """
    # Measure the length of each prompt
    lengths = []
    for i in range(len(dataframe)):
        prompt = dataframe.iloc[i][prompt_key]
        # Handle different prompt formats
        if isinstance(prompt, str):
            # For string prompts, tokenize directly
            tokens = tokenizer.encode(prompt)
        elif isinstance(prompt, list) and all(isinstance(msg, dict) for msg in prompt):
            # For chat format, apply chat template first
            formatted = tokenizer.apply_chat_template(prompt, add_generation_prompt=True, tokenize=True)
            tokens = formatted
        elif isinstance(prompt, dict):
            # For dictionary format, convert to string first
            tokens = tokenizer.encode(str(prompt))
        else:
            # Fallback for other formats
            tokens = tokenizer.encode(str(prompt))

        lengths.append(len(tokens))

    # Create buckets
    _, index_to_bucket = create_length_buckets(
        base_name=base_name,
        lengths=lengths,
        num_buckets=num_buckets,
        method=method
    )

    # Add bucket assignments to the dataframe
    bucket_assignments = [index_to_bucket[i] for i in range(len(dataframe))]
    df_with_buckets = dataframe.copy()
    df_with_buckets[bucket_column] = bucket_assignments

    # Log bucket distribution
    bucket_counts = df_with_buckets[bucket_column].value_counts().to_dict()
    logger.info(f"Length bucket distribution: {bucket_counts}")

    return df_with_buckets
