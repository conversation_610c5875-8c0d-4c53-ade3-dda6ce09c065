"""
Curriculum Learning for PPO Training

This module provides curriculum learning utilities for training on various data sources
with learnability-based sampling. It implements a Bayesian approach to estimate task learnability
and dynamically adjust sampling weights to focus on tasks with the highest learning potential.

How to use:
1. Set 'enable_curriculum_learning: True' in your config file
2. Set 'data_source_key' to the column in your dataset that identifies different data sources/tasks
3. The system will automatically track performance on different tasks and adjust sampling weights

The curriculum learning system includes:
- LearnabilityEstimator: Tracks performance metrics for individual tasks
- CurriculumController: Manages multiple estimators and computes optimal sampling weights
- CurriculumSampler: Implements the torch.utils.data.Sampler interface for curriculum-based sampling

The system uses a combination of Upper Confidence Bound (UCB) and Thompson sampling to balance
exploration and exploitation, focusing on tasks that are challenging but achievable.
"""

import numpy as np
import torch
import logging
import random
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple, Any, Union
from scipy.stats import beta as beta_dist
from scipy.stats import norm as norm_dist
from collections import defaultdict

logger = logging.getLogger(__name__)

@dataclass
class LearnabilityEstimator:
    """Learnability estimator for curriculum learning. 跟踪单个数据分布的性能指标"""
    # Beta distribution parameters (for reward modeling)
    alpha: float = 1.0
    beta: float = 1.0
    # Normal distribution parameters (for advantage function modeling)
    mu: float = 0.0
    # Additional statistics
    n_samples: int = 0
    total_reward: float = 0.0
    # Sliding window for recent performance tracking
    window_size: int = 300  # sliding window size
    recent_rewards: List[float] = field(default_factory=list)
    recent_advantages: List[float] = field(default_factory=list)
    # Perplexity tracking for new learnability score
    recent_logppls: List[float] = field(default_factory=list)
    recent_correctness: List[float] = field(default_factory=list)  # C values (0-1)
    norm_logppl: float = 0.5  # Default normalized log perplexity (middle value)
    correctness_rate: float = 0.5  # Default correctness rate (middle value)
    # Influence function scores for bandit updates
    recent_influence_scores: List[float] = field(default_factory=list)
    mean_influence_score: float = 0.0  # Mean influence score
    use_influence_functions: bool = False  # Whether to use influence functions instead of perplexity

    def update(self, rewards: np.ndarray, advantages: np.ndarray, logppls: Optional[np.ndarray] = None, correctness: Optional[np.ndarray] = None, influence_scores: Optional[np.ndarray] = None):
        """Update based on new data.

        Args:
            rewards: Reward values for each sample
            advantages: Advantage values for each sample
            logppls: Log perplexity values for each sample (optional)
            correctness: Correctness values (0-1) for each sample (optional)
            influence_scores: Influence function scores for each sample (optional)
        """
        n = len(rewards)
        if n == 0:
            return

        # Update recent rewards and advantages
        self.recent_rewards.extend(rewards.tolist())
        self.recent_advantages.extend(advantages.tolist())

        # Update log perplexity and correctness if provided
        if logppls is not None:
            self.recent_logppls.extend(logppls.tolist())
        if correctness is not None:
            self.recent_correctness.extend(correctness.tolist())
        if influence_scores is not None:
            self.recent_influence_scores.extend(influence_scores.tolist())

        # Maintain window size for all metrics
        if len(self.recent_rewards) > self.window_size:
            self.recent_rewards = self.recent_rewards[-self.window_size:]
            self.recent_advantages = self.recent_advantages[-self.window_size:]
            if self.recent_logppls:
                self.recent_logppls = self.recent_logppls[-self.window_size:]
            if self.recent_correctness:
                self.recent_correctness = self.recent_correctness[-self.window_size:]
            if self.recent_influence_scores:
                self.recent_influence_scores = self.recent_influence_scores[-self.window_size:]

        # Update Beta distribution parameters based on successes/failures
        successes = np.sum(rewards >= 1)  # Consider reward >= 1 as success # TODO: make this configurable
        failures = len(rewards) - successes

        # Accumulate Beta parameters (without resetting)
        self.alpha += successes
        self.beta += failures

        # Update normal distribution using window data with absolute advantages
        window_advantages = np.abs(np.array(self.recent_advantages)) # Take absolute value here
        self.mu = np.mean(window_advantages)

        # Update normalized log perplexity if we have data
        if self.recent_logppls:
            # Normalize log perplexity to 0-1 range (higher is more difficult)
            # We use min-max normalization within the window
            logppls_array = np.array(self.recent_logppls)
            min_logppl = np.min(logppls_array)
            max_logppl = np.max(logppls_array)
            if max_logppl > min_logppl:  # Avoid division by zero
                self.norm_logppl = np.mean((logppls_array - min_logppl) / (max_logppl - min_logppl))
            else:
                self.norm_logppl = 0.1  # Default to middle value if all values are the same

        # Update correctness rate if we have data
        if self.recent_correctness:
            self.correctness_rate = np.mean(np.array(self.recent_correctness))

        # Update influence score statistics if we have data
        if self.recent_influence_scores:
            self.mean_influence_score = np.mean(np.array(self.recent_influence_scores))

        # Update statistics
        self.n_samples += n
        self.total_reward += np.sum(rewards)

    @property
    def expected_value(self) -> float:
        """Calculate expected value using influence functions or original reward-advantage formula.

        Uses influence function scores when available and enabled, otherwise falls back
        to the original reward-advantage formula for backward compatibility.

        Returns:
            float: Expected value in range (0, 1) for bandit algorithm
        """
        # Use influence function scores if enabled and available
        print(f"注意 self.use_influence_functions: {self.use_influence_functions}, self.recent_influence_scores: {self.recent_influence_scores}")
        if self.use_influence_functions and self.recent_influence_scores:
            # Influence scores can be negative (harmful) or positive (beneficial)
            # We normalize them to a reasonable range for bandit algorithms
            influence_score = self.mean_influence_score
            # Apply sigmoid transformation to map to (0, 1) range
            # Higher influence scores (more beneficial) -> higher expected value
            normalized_score = 1.0 / (1.0 + np.exp(-influence_score))
            return normalized_score

        # Fall back to the original reward-advantage formula
        else:
            reward_rate = self.alpha / (self.alpha + self.beta)
            return reward_rate * self.mu

    @property
    def recent_reward_rate(self) -> float:
        """Calculate the recent success rate within the sliding window."""
        if not self.recent_rewards:
            return 0.0
        window_rewards = np.array(self.recent_rewards)
        successes = np.sum(window_rewards >= 1)  # Consider reward >= 1 as success
        return successes / len(window_rewards) if len(window_rewards) > 0 else 0.0


class CurriculumController:
    """Controller for task curriculum learning.管理多个估计器并计算最佳采样权重"""
    def __init__(self, data_sources: List[str], trend_window: int = 50, initial_weights: Optional[Dict[str, float]] = None):
        """Initialize the curriculum controller.

        Args:
            data_sources: List of data source names
            trend_window: Window size for trend analysis
            initial_weights: Optional dictionary of initial weights for each source
        """
        self.data_sources = list(set(data_sources))  # Ensure unique sources
        self.trend_window = trend_window

        # Initialize estimators with initial weights if provided
        self.estimators = {
            source: LearnabilityEstimator()
            for source in self.data_sources
        }

        # Store weight history for each source
        self.weight_history = {
            source: []
            for source in self.data_sources
        }

        # If initial weights provided, store them in weight history
        if initial_weights:
            for source in self.data_sources:
                if source in initial_weights:
                    self.weight_history[source].append(initial_weights[source])

    def compute_trend(self, history):
        """Calculate trend using linear regression on weight history."""
        if len(history) < 2:
            return 0.0

        # Use only the most recent trend_window data points
        history = history[-self.trend_window:]
        n = len(history)
        x = np.arange(n)
        y = np.array(history)

        # Calculate linear regression slope
        x_mean = x.mean()
        y_mean = y.mean()

        # Avoid division by zero
        denominator = np.sum((x - x_mean) ** 2)
        if denominator == 0:
            return 0.0

        slope = np.sum((x - x_mean) * (y - y_mean)) / denominator
        return slope

    def compute_sampling_weights(self) -> Dict[str, float]:
        """Compute sampling weights for each data source."""
        stats = self.get_source_stats()

        # Calculate UCB scores for each source
        base_scores = []
        source_to_index = {}

        # Calculate total samples across all sources
        total_samples = sum(stat['n_samples'] for stat in stats.values())
        total_samples = max(1, total_samples)  # Avoid division by zero

        for i, source in enumerate(self.data_sources):
            source_to_index[source] = i
            stat = stats[source]

            # Weight parameters
            uncertainty_weight = 1.0  # uncertainty term weight
            exploration_weight = 1.0  # exploration term weight

            # Base UCB score using the advantage mean
            ucb_score = stat['advantage_mean']

            # Add exploration bonus (more exploration for less sampled sources)
            exploration_bonus = exploration_weight * np.sqrt(
                2 * np.log(total_samples + 1) / (stat['n_samples'] + 1)
            )
            ucb_score += exploration_bonus

            base_scores.append(ucb_score)

            logger.debug(f"Source {source} UCB calculation:")
            logger.debug(f"  Advantage mean: {stat['advantage_mean']:.3f}")
            logger.debug(f"  Exploration bonus: {exploration_bonus:.3f}")
            logger.debug(f"  Final UCB score: {ucb_score:.3f}")

        # Convert scores to a probability distribution using softmax
        temperature = 0.1  # controls distribution smoothness
        scores_array = np.array(base_scores)

        # Softmax calculation with temperature
        # Subtract max for numerical stability
        if len(scores_array) > 0:
            max_score = np.max(scores_array)
            exp_scores = np.exp((scores_array - max_score) / temperature)
            weights_array = exp_scores / np.sum(exp_scores)

            # Create a dictionary mapping sources to weights
            weights = {source: weights_array[source_to_index[source]] for source in self.data_sources}
        else:
            # Equal weights if no scores available
            weights = {source: 1.0/len(self.data_sources) for source in self.data_sources}

        # Update weight history
        for source, weight in weights.items():
            self.weight_history[source].append(weight)
            # Keep history within window size
            if len(self.weight_history[source]) > self.trend_window:
                self.weight_history[source] = self.weight_history[source][-self.trend_window:]

        return weights

    def get_source_stats(self) -> Dict[str, Dict[str, float]]:
        """Get statistics for each data source."""
        stats = {}
        for source in self.data_sources:
            estimator = self.estimators[source]
            stats[source] = {
                'expected_value': estimator.expected_value,
                'reward_rate': estimator.alpha / (estimator.alpha + estimator.beta),
                'recent_reward_rate': estimator.recent_reward_rate,
                'advantage_mean': estimator.mu,
                'n_samples': estimator.n_samples,
                'mean_influence_score': getattr(estimator, 'mean_influence_score', 0.0),
                'use_influence_functions': getattr(estimator, 'use_influence_functions', False),
                # Keep perplexity fields for backward compatibility and debugging
                'norm_logppl': getattr(estimator, 'norm_logppl', 0.1),
                'correctness_rate': getattr(estimator, 'correctness_rate', 0.1)
            }
        return stats


class CurriculumSampler:
    """Sampler that implements curriculum learning based on task difficulty."""
    def __init__(self,
                 dataset,
                 data_source_key: str = 'data_source',
                 batch_size: int = 1,
                 seed: Optional[int] = None,
                 replacement: bool = True):
        """Initialize the curriculum sampler.

        Args:
            dataset: The dataset to sample from
            data_source_key: The key in the dataset items that identifies the data source
            batch_size: The batch size for sampling
            seed: Random seed for reproducibility
            replacement: Whether to sample with replacement (default: True)
        """
        self.dataset = dataset
        self.data_source_key = data_source_key
        self.batch_size = batch_size
        self.rng = random.Random(seed)
        self.replacement = replacement

        # For sampling without replacement
        self.available_indices = defaultdict(list)
        self.invalid_sources = set()

        # Group dataset items by data source
        self.source_indices = defaultdict(list)

        # Keep track of problematic indices for debugging
        self.problematic_indices = []

        # Track selected data sources and corresponding steps
        self.source_selection_history = {
            'steps': [],
            'sources': []
        }

        # Pre-check prompt format validity to avoid sampling problematic examples
        for i in range(len(dataset)):
            try:
                item = dataset[i]
                if isinstance(item, dict) and self.data_source_key in item:
                    source = item[self.data_source_key]

                    # Verify that this sample won't cause problems during training
                    # Try to access the prompt from the dataset - this will verify format
                    # We don't need to actually use the result, just check it works
                    _ = dataset[i]

                    # If successful, add to source indices
                    self.source_indices[source].append(i)
                else:
                    # Handle items without a data source by creating a default source
                    # But still verify it won't cause problems
                    _ = dataset[i]
                    self.source_indices["default"].append(i)
            except Exception as e:
                # If any error occurs during checking, track it but don't include the sample
                self.problematic_indices.append((i, str(e)))
                print(f"Warning: index {i} skipped due to error: {e}")
                continue

        # Report number of problematic indices
        if self.problematic_indices:
            print(f"Warning: {len(self.problematic_indices)} samples excluded from curriculum sampler due to format issues")

        # If no valid sources were found, create a default one with valid samples
        if not self.source_indices:
            print("Warning: No valid source groups found, creating default source with all valid samples")

            # Identify all valid indices by directly testing each sample
            valid_indices = []
            for i in range(len(dataset)):
                if i not in [x[0] for x in self.problematic_indices]:
                    try:
                        _ = dataset[i]
                        valid_indices.append(i)
                    except:
                        pass

            self.source_indices["default"] = valid_indices

        # # Calculate total valid samples across all sources
        # total_samples = sum(len(indices) for indices in self.source_indices.values())

        # # Initialize weights based on proportion of samples in each source
        # if total_samples > 0:
        #     self._weights = {
        #         source: len(indices) / total_samples
        #         for source, indices in self.source_indices.items()
        #     }
        # else:
        #     # Fallback to equal weights if no valid samples
        #     self._weights = {
        #         source: 1.0 / len(self.source_indices)
        #         for source in self.source_indices
        #     }

        # Initialize the curriculum controller with all data sources
        self.curriculum_controller = CurriculumController(
            data_sources=list(self.source_indices.keys())
            # initial_weights=self._weights  # Pass initial weights to controller
        )

    def update_weights(self,
                      source_rewards: Dict[str, List[float]],
                      source_advantages: Dict[str, List[float]],
                      source_logppls: Optional[Dict[str, List[float]]] = None,
                      source_correctness: Optional[Dict[str, List[float]]] = None,
                      source_influence_scores: Optional[Dict[str, List[float]]] = None):
        """Update the curriculum controller with new rewards, advantages, and optional data.

        Args:
            source_rewards: Dictionary mapping sources to lists of reward values
            source_advantages: Dictionary mapping sources to lists of advantage values
            source_logppls: Optional dictionary mapping sources to lists of log perplexity values
            source_correctness: Optional dictionary mapping sources to lists of correctness values (0-1)
            source_influence_scores: Optional dictionary mapping sources to lists of influence scores
        """
        for source in source_rewards:
            if source in self.curriculum_controller.estimators:
                rewards = np.array(source_rewards[source])
                advantages = np.array(source_advantages[source])

                # Get log perplexity, correctness, and influence scores for this source if available
                logppls = np.array(source_logppls.get(source, [])) if source_logppls else None
                correctness = np.array(source_correctness.get(source, [])) if source_correctness else None
                influence_scores = np.array(source_influence_scores.get(source, [])) if source_influence_scores else None

                # Update the estimator with all available data
                self.curriculum_controller.estimators[source].update(rewards, advantages, logppls, correctness, influence_scores)

        # Recompute sampling weights
        self._weights = self.curriculum_controller.compute_sampling_weights()

    def enable_influence_functions(self, enable: bool = True):
        """Enable or disable influence function-based scoring for all estimators.

        Args:
            enable: Whether to enable influence function scoring
        """
        for estimator in self.curriculum_controller.estimators.values():
            estimator.use_influence_functions = enable
        logger.info(f"Influence functions {'enabled' if enable else 'disabled'} for curriculum learning")

    def get_source_stats(self):
        """Get statistics for each data source by delegating to the curriculum controller."""
        stats = self.curriculum_controller.get_source_stats()

        # Add sampling weights to the statistics
        for source in stats:
            stats[source]['sampling_weight'] = self._weights.get(source, 0.0)

        # Calculate weight trend if possible (could add this as a separate field)
        weight_trend = 0.0
        if hasattr(self.curriculum_controller, 'compute_trend') and hasattr(self.curriculum_controller, 'weight_history'):
            trends = {}
            for source, history in self.curriculum_controller.weight_history.items():
                if history:  # Only compute trend if we have history
                    trends[source] = self.curriculum_controller.compute_trend(history)

            # Average trend across all sources
            if trends:
                weight_trend = sum(trends.values()) / len(trends)

        # Add overall stats
        stats['weight_trend'] = weight_trend

        # Add source selection history
        stats['source_selection_history'] = self.source_selection_history

        return stats

    def record_source_selection(self, step: int, source: str):
        """Record which data source was selected at a given step.

        Args:
            step: The current training step
            source: The selected data source
        """
        self.source_selection_history['steps'].append(step)
        self.source_selection_history['sources'].append(source)

    def __iter__(self):
        """Create an iterator for sampling from the dataset."""
        # Convert weights to a list of (source, weight) pairs for selection
        sources = list(self._weights.keys())
        weights = [self._weights[source] for source in sources]

        # Yield indices in batches
        indices = []

        total_batches = len(self)
        batches_yielded = 0

        # Keep track of errors during sampling
        sampling_errors = 0
        max_errors = 100  # Maximum number of errors before giving up

        # For sampling without replacement, initialize available indices
        if not self.replacement:
            self.available_indices = {source: list(source_indices) for source, source_indices in self.source_indices.items()}
            self.invalid_sources = set()

        while batches_yielded < total_batches and sampling_errors < max_errors:
            # Sample a data source according to weights
            if sources and weights:
                try:
                    # Determine valid sources based on sampling method
                    if self.replacement:
                        # With replacement: sources with non-zero indices
                        valid_sources = [s for s in sources if len(self.source_indices[s]) > 0]
                    else:
                        # Without replacement: sources with available indices and not marked invalid
                        valid_sources = [s for s in sources if s not in self.invalid_sources and len(self.available_indices[s]) > 0]

                    if not valid_sources:
                        if not self.replacement and all(s in self.invalid_sources or len(self.available_indices[s]) == 0 for s in sources):
                            # All sources exhausted in no-replacement mode, reset for next epoch
                            print("All sources exhausted, resetting available indices for next epoch")
                            self.available_indices = {source: list(source_indices) for source, source_indices in self.source_indices.items()}
                            self.invalid_sources = set()
                            valid_sources = [s for s in sources if len(self.source_indices[s]) > 0]
                        else:
                            # If no valid sources, skip this iteration
                            sampling_errors += 1
                            continue

                    valid_weights = [self._weights[s] for s in valid_sources]
                    # Normalize weights
                    weight_sum = sum(valid_weights)
                    if weight_sum > 0:
                        valid_weights = [w/weight_sum for w in valid_weights]
                    else:
                        valid_weights = [1.0/len(valid_sources) for _ in valid_sources]

                    source = self.rng.choices(valid_sources, weights=valid_weights, k=1)[0]

                    # Record the selected source (step will be set by the trainer)
                    # We use -1 as a placeholder for the step, which will be updated later
                    self.record_source_selection(-1, source)

                    # Sample an index based on sampling method
                    if self.replacement:
                        # With replacement: sample from all source indices
                        if not self.source_indices[source]:
                            sampling_errors += 1
                            continue
                        idx = self.rng.choice(self.source_indices[source])
                    else:
                        # Without replacement: sample from available indices
                        if not self.available_indices[source]:
                            # Mark this source as invalid if it has no more available indices
                            self.invalid_sources.add(source)
                            sampling_errors += 1
                            continue
                        idx = self.rng.choice(self.available_indices[source])
                        # Remove the sampled index from available indices
                        self.available_indices[source].remove(idx)
                        # Mark source as invalid if it has no more available indices
                        if not self.available_indices[source]:
                            self.invalid_sources.add(source)

                    # Verify this index still works (double-check)
                    _ = self.dataset[idx]

                    indices.append(idx)
                except Exception as e:
                    # If error during sampling, try again
                    sampling_errors += 1
                    print(f"Error during sampling: {e}")
                    continue
            else:
                # Fallback to random sampling if no sources/weights
                try:
                    if self.replacement:
                        # With replacement: sample from all valid indices
                        all_valid_indices = []
                        for source_indices in self.source_indices.values():
                            all_valid_indices.extend(source_indices)
                    else:
                        # Without replacement: sample from available indices
                        all_valid_indices = []
                        for source, indices in self.available_indices.items():
                            if source not in self.invalid_sources:
                                all_valid_indices.extend(indices)

                        # If all indices exhausted, reset for next epoch
                        if not all_valid_indices:
                            self.available_indices = {source: list(source_indices) for source, source_indices in self.source_indices.items()}
                            self.invalid_sources = set()
                            for source_indices in self.source_indices.values():
                                all_valid_indices.extend(source_indices)

                    if not all_valid_indices:
                        sampling_errors += 1
                        continue

                    idx = self.rng.choice(all_valid_indices)

                    # If sampling without replacement, remove the index from available indices
                    if not self.replacement:
                        for source, indices in self.available_indices.items():
                            if idx in indices:
                                indices.remove(idx)
                                # Mark source as invalid if it has no more available indices
                                if not indices:
                                    self.invalid_sources.add(source)
                                break

                    indices.append(idx)
                except Exception as e:
                    sampling_errors += 1
                    print(f"Error during fallback sampling: {e}")
                    continue

            # When we have a full batch, yield it
            if len(indices) == self.batch_size:
                yield from indices
                indices = []
                batches_yielded += 1

        # If we hit max errors, warn the user
        if sampling_errors >= max_errors:
            print(f"WARNING: Hit maximum number of sampling errors ({max_errors}). Some batches may be incomplete.")

    def __len__(self):
        """Return the total number of batches."""
        return len(self.dataset)
