"""
Influence Function Computation Module

This module implements influence functions for curriculum learning, replacing perplexity-based
bandit updates with influence function calculations.

The influence function measures how a training sample z affects the loss on validation set D_r:
I_θ(D_r, z) = -∇_θ L(θ, D_r) (H_θ + λI)^(-1) ∇_θ L(θ, z)

Where:
- I_θ(D_r, z) is the influence function
- L(θ, D_r) is the loss function evaluated on validation set D_r  
- L(θ, z) is the loss function evaluated on training sample z
- θ represents the model parameters
- H_θ is the Hessian matrix of the loss with respect to parameters
- λ is a regularization parameter
- I is the identity matrix
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Union
import numpy as np
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)


class InfluenceFunctionCalculator:
    """
    Calculator for influence functions with K-FAC Hessian approximation.
    
    This class computes influence scores that measure how training samples
    affect validation performance, replacing perplexity-based scoring.
    """
    
    def __init__(self, 
                 model: nn.Module,
                 regularization_lambda: float = 1e-3,
                 damping_factor: float = 1e-3,
                 use_kfac: bool = True,
                 max_samples_per_batch: int = 32):
        """
        Initialize the influence function calculator.
        
        Args:
            model: The neural network model
            regularization_lambda: Regularization parameter λ for (H_θ + λI)^(-1)
            damping_factor: Damping factor for numerical stability
            use_kfac: Whether to use K-FAC approximation for Hessian
            max_samples_per_batch: Maximum samples to process in one batch
        """
        self.model = model
        self.regularization_lambda = regularization_lambda
        self.damping_factor = damping_factor
        self.use_kfac = use_kfac
        self.max_samples_per_batch = max_samples_per_batch
        
        # Cache for gradients and Hessian information
        self.validation_gradients = None
        self.hessian_info = None
        self.device = next(model.parameters()).device
        
    def compute_gradients(self,
                         batch: Dict[str, torch.Tensor],
                         loss_fn: callable) -> Dict[str, torch.Tensor]:
        """
        Compute gradients of the loss with respect to model parameters.

        Args:
            batch: Input batch containing input_ids, attention_mask, etc.
            loss_fn: Loss function that takes model output and returns loss

        Returns:
            Dictionary mapping parameter names to gradient tensors
        """
        # Clear any existing gradients with memory efficiency
        self.model.zero_grad(set_to_none=True)

        try:
            # Forward pass
            outputs = self.model(**batch)
            loss = loss_fn(outputs, batch)

            # Backward pass to compute gradients
            loss.backward()

            # Collect gradients and detach immediately
            gradients = {}
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    gradients[name] = param.grad.clone().detach()

            # Clear gradients immediately after collection
            self.model.zero_grad(set_to_none=True)

            return gradients

        except torch.cuda.OutOfMemoryError:
            # Clear everything on OOM
            self.model.zero_grad(set_to_none=True)
            torch.cuda.empty_cache()
            raise
    
    def compute_validation_gradients(self, 
                                   validation_batch: Dict[str, torch.Tensor],
                                   loss_fn: callable) -> None:
        """
        Compute and cache gradients on validation set D_r.
        
        Args:
            validation_batch: Validation data batch
            loss_fn: Loss function
        """
        self.validation_gradients = self.compute_gradients(validation_batch, loss_fn)
        logger.debug(f"Computed validation gradients for {len(self.validation_gradients)} parameters")
    
    def flatten_gradients(self, gradients: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Flatten gradient dictionary into a single vector.
        
        Args:
            gradients: Dictionary of parameter gradients
            
        Returns:
            Flattened gradient vector
        """
        grad_list = []
        for name in sorted(gradients.keys()):
            grad_list.append(gradients[name].view(-1))
        return torch.cat(grad_list)
    
    def compute_hvp(self,
                   vector: torch.Tensor,
                   batch: Dict[str, torch.Tensor],
                   loss_fn: callable) -> torch.Tensor:
        """
        Compute Hessian-vector product H_θ * vector efficiently with memory management.

        Uses the identity: H_θ * v = ∇_θ (∇_θ L(θ) · v)

        Args:
            vector: Vector to multiply with Hessian
            batch: Input batch for computing Hessian
            loss_fn: Loss function

        Returns:
            Hessian-vector product
        """
        try:
            # First compute gradients
            gradients = self.compute_gradients(batch, loss_fn)
            flat_gradients = self.flatten_gradients(gradients)

            # Clear gradients dict to save memory
            del gradients
            torch.cuda.empty_cache()

            # Compute gradient-vector product
            gvp = torch.sum(flat_gradients * vector)

            # Clear flat_gradients
            del flat_gradients
            torch.cuda.empty_cache()

            # Compute second-order gradients (Hessian-vector product)
            self.model.zero_grad(set_to_none=True)
            gvp.backward()

            hvp_list = []
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    hvp_list.append(param.grad.view(-1).clone().detach())

            # Clear gradients immediately
            self.model.zero_grad(set_to_none=True)

            return torch.cat(hvp_list)

        except torch.cuda.OutOfMemoryError:
            # Clear everything on OOM
            self.model.zero_grad(set_to_none=True)
            torch.cuda.empty_cache()
            raise
    
    def solve_inverse_hvp(self,
                         vector: torch.Tensor,
                         batch: Dict[str, torch.Tensor],
                         loss_fn: callable,
                         max_iterations: int = 50,  # Reduced default iterations
                         tolerance: float = 1e-4) -> torch.Tensor:  # Relaxed tolerance
        """
        Solve (H_θ + λI)^(-1) * vector using memory-efficient conjugate gradient method.

        Args:
            vector: Vector to solve for
            batch: Batch for Hessian computation
            loss_fn: Loss function
            max_iterations: Maximum CG iterations
            tolerance: Convergence tolerance

        Returns:
            Solution to (H_θ + λI)^(-1) * vector
        """
        # Use smaller batch for HVP computation if needed
        batch_size = batch['input_ids'].size(0) if 'input_ids' in batch else 1
        if batch_size > 2:
            # Use subset for HVP computation to save memory
            hvp_batch = {}
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    hvp_batch[key] = value[:1]  # Use only first sample
                else:
                    hvp_batch[key] = value
        else:
            hvp_batch = batch

        # Initialize solution
        x = torch.zeros_like(vector)
        r = vector.clone()
        p = r.clone()
        rsold = torch.sum(r * r)

        for i in range(max_iterations):
            try:
                # Clear cache before HVP computation
                torch.cuda.empty_cache()

                # Compute Hessian-vector product with regularization
                hvp = self.compute_hvp(p, hvp_batch, loss_fn)
                hvp += self.regularization_lambda * p  # Add λI * p

                alpha = rsold / torch.sum(p * hvp)
                x += alpha * p
                r -= alpha * hvp
                rsnew = torch.sum(r * r)

                # Clear intermediate tensors
                del hvp
                torch.cuda.empty_cache()

                if torch.sqrt(rsnew) < tolerance:
                    logger.debug(f"CG converged in {i+1} iterations")
                    break

                beta = rsnew / rsold
                p = r + beta * p
                rsold = rsnew

            except torch.cuda.OutOfMemoryError:
                logger.warning(f"OOM in CG iteration {i}, breaking early")
                break

        return x
    
    def compute_influence_score(self,
                              training_sample: Dict[str, torch.Tensor],
                              validation_batch: Dict[str, torch.Tensor],
                              loss_fn: callable) -> float:
        """
        Compute influence score for a single training sample with memory management.

        Args:
            training_sample: Single training sample
            validation_batch: Validation batch for computing validation gradients
            loss_fn: Loss function

        Returns:
            Influence score (scalar)
        """
        try:
            # Clear cache before computation
            torch.cuda.empty_cache()

            # Compute validation gradients if not cached
            if self.validation_gradients is None:
                self.compute_validation_gradients(validation_batch, loss_fn)

            # Compute training sample gradients
            training_gradients = self.compute_gradients(training_sample, loss_fn)

            # Flatten gradients
            val_grad_flat = self.flatten_gradients(self.validation_gradients)
            train_grad_flat = self.flatten_gradients(training_gradients)

            # Clear intermediate gradients to save memory
            del training_gradients
            torch.cuda.empty_cache()

            # Solve (H_θ + λI)^(-1) * ∇_θ L(θ, z)
            inverse_hvp = self.solve_inverse_hvp(train_grad_flat, validation_batch, loss_fn)

            # Compute influence: -∇_θ L(θ, D_r) · (H_θ + λI)^(-1) ∇_θ L(θ, z)
            influence_score = -torch.sum(val_grad_flat * inverse_hvp).item()

            # Clear intermediate tensors
            del val_grad_flat, train_grad_flat, inverse_hvp
            torch.cuda.empty_cache()

            print("成功计算影响函数")
            return influence_score

        except torch.cuda.OutOfMemoryError as e:
            logger.warning(f"CUDA OOM in influence score computation: {e}")
            # Clear everything and return default score
            torch.cuda.empty_cache()
            return 0.0
        except Exception as e:
            logger.warning(f"Error in influence score computation: {e}")
            return 0.0

    def compute_influence_scores_batch(self,
                                     training_samples: List[Dict[str, torch.Tensor]],
                                     validation_batch: Dict[str, torch.Tensor],
                                     loss_fn: callable) -> List[float]:
        """
        Compute influence scores for a batch of training samples.

        Args:
            training_samples: List of training samples
            validation_batch: Validation batch
            loss_fn: Loss function

        Returns:
            List of influence scores
        """
        influence_scores = []

        # Process in smaller batches to manage memory
        for i in range(0, len(training_samples), self.max_samples_per_batch):
            batch_samples = training_samples[i:i + self.max_samples_per_batch]

            for sample in batch_samples:
                try:
                    score = self.compute_influence_score(sample, validation_batch, loss_fn)
                    influence_scores.append(score)
                except Exception as e:
                    logger.warning(f"Failed to compute influence score for sample {i}: {e}")
                    influence_scores.append(0.0)  # Default score on failure

        return influence_scores


class KFACInfluenceFunctionCalculator(InfluenceFunctionCalculator):
    """
    Influence function calculator with K-FAC (Kronecker-Factored Approximate Curvature)
    Hessian approximation for improved computational efficiency.
    """

    def __init__(self, *args, **kwargs):
        # 提取KFACInfluenceFunctionCalculator特有的参数
        self.kfac_damping = kwargs.pop('kfac_damping', 1e-3)
        # 调用父类构造函数，不会再传递kfac_damping参数
        super().__init__(*args, **kwargs)
        self.kfac_factors = {}

    def extract_kfac_factors(self, batch: Dict[str, torch.Tensor], loss_fn: callable):
        """
        Extract K-FAC factors for Kronecker product approximation of the Hessian.

        For linear layers: H ≈ A ⊗ G where A is input covariance, G is gradient covariance

        Args:
            batch: Input batch
            loss_fn: Loss function
        """
        self.kfac_factors = {}
        self._debug_info = {}  # Store debug information

        # Register hooks to capture activations and gradients
        def forward_hook(module, input, output):
            if isinstance(module, nn.Linear):
                try:
                    # Store input activations for A factor
                    activations = input[0].detach()
                    module_id = id(module)

                    # Debug: Log original tensor shapes
                    self._debug_info[f"{module_id}_input_shape"] = activations.shape
                    logger.debug(f"Forward hook - Module {module_id}: input shape {activations.shape}")

                    # Robust reshaping to 2D
                    original_shape = activations.shape
                    if len(original_shape) == 2:
                        # Already 2D: [batch_size, input_dim]
                        activations_2d = activations
                    elif len(original_shape) == 3:
                        # 3D: [batch_size, seq_len, input_dim] -> [batch_size * seq_len, input_dim]
                        activations_2d = activations.view(-1, original_shape[-1])
                    elif len(original_shape) == 4:
                        # 4D: [batch_size, heads, seq_len, input_dim] -> [batch_size * heads * seq_len, input_dim]
                        activations_2d = activations.view(-1, original_shape[-1])
                    else:
                        # General case: flatten all but last dimension
                        activations_2d = activations.view(-1, original_shape[-1])

                    logger.debug(f"Forward hook - Module {module_id}: reshaped to {activations_2d.shape}")

                    # Validate dimensions
                    if activations_2d.dim() != 2:
                        logger.warning(f"Forward hook - Module {module_id}: Failed to reshape to 2D, got {activations_2d.shape}")
                        return

                    # Store original activations without bias for weight computation
                    A_weight = torch.mm(activations_2d.t(), activations_2d) / activations_2d.shape[0]
                    self.kfac_factors[f"{module_id}_A_weight"] = A_weight
                    logger.debug(f"Forward hook - Module {module_id}: A_weight shape {A_weight.shape}")

                    # Store activations with bias term for bias computation if bias exists
                    if module.bias is not None:
                        ones = torch.ones(activations_2d.shape[0], 1, device=activations_2d.device, dtype=activations_2d.dtype)
                        activations_with_bias = torch.cat([activations_2d, ones], dim=1)
                        A_bias = torch.mm(activations_with_bias.t(), activations_with_bias) / activations_with_bias.shape[0]
                        self.kfac_factors[f"{module_id}_A_bias"] = A_bias
                        logger.debug(f"Forward hook - Module {module_id}: A_bias shape {A_bias.shape}")

                except Exception as e:
                    logger.error(f"Forward hook error for module {id(module)}: {e}")
                    logger.error(f"Input shape: {input[0].shape if input else 'None'}")

        def backward_hook(module, grad_input, grad_output):
            if isinstance(module, nn.Linear):
                try:
                    # Store output gradients for G factor
                    if grad_output[0] is None:
                        logger.warning(f"Backward hook - Module {id(module)}: grad_output is None")
                        return

                    grad_out = grad_output[0].detach()
                    module_id = id(module)

                    # Debug: Log original tensor shapes
                    self._debug_info[f"{module_id}_grad_shape"] = grad_out.shape
                    logger.debug(f"Backward hook - Module {module_id}: grad shape {grad_out.shape}")

                    # Robust reshaping to 2D
                    original_shape = grad_out.shape
                    if len(original_shape) == 2:
                        # Already 2D: [batch_size, output_dim]
                        grad_out_2d = grad_out
                    elif len(original_shape) == 3:
                        # 3D: [batch_size, seq_len, output_dim] -> [batch_size * seq_len, output_dim]
                        grad_out_2d = grad_out.view(-1, original_shape[-1])
                    elif len(original_shape) == 4:
                        # 4D: [batch_size, heads, seq_len, output_dim] -> [batch_size * heads * seq_len, output_dim]
                        grad_out_2d = grad_out.view(-1, original_shape[-1])
                    else:
                        # General case: flatten all but last dimension
                        grad_out_2d = grad_out.view(-1, original_shape[-1])

                    logger.debug(f"Backward hook - Module {module_id}: reshaped to {grad_out_2d.shape}")

                    # Validate dimensions
                    if grad_out_2d.dim() != 2:
                        logger.warning(f"Backward hook - Module {module_id}: Failed to reshape to 2D, got {grad_out_2d.shape}")
                        return

                    G = torch.mm(grad_out_2d.t(), grad_out_2d) / grad_out_2d.shape[0]
                    self.kfac_factors[f"{module_id}_G"] = G
                    logger.debug(f"Backward hook - Module {module_id}: G shape {G.shape}")

                except Exception as e:
                    logger.error(f"Backward hook error for module {id(module)}: {e}")
                    logger.error(f"Grad output shape: {grad_output[0].shape if grad_output and grad_output[0] is not None else 'None'}")

        # Register hooks
        hooks = []
        linear_modules = []
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Linear):
                linear_modules.append((name, module))
                hooks.append(module.register_forward_hook(forward_hook))
                hooks.append(module.register_backward_hook(backward_hook))

        logger.debug(f"Registered hooks for {len(linear_modules)} linear modules")
        for name, module in linear_modules:
            logger.debug(f"  {name}: {module}")

        try:
            # Clear any existing factors
            self.kfac_factors.clear()

            # Forward and backward pass to collect factors
            logger.debug("Starting forward pass...")
            outputs = self.model(**batch)

            logger.debug("Starting loss computation...")
            loss = loss_fn(outputs, batch)
            logger.debug(f"Loss: {loss.item()}")

            logger.debug("Starting backward pass...")
            loss.backward()

            # Log extracted factors
            logger.debug(f"Extracted {len(self.kfac_factors)} K-FAC factors:")
            for key, factor in self.kfac_factors.items():
                logger.debug(f"  {key}: {factor.shape}")

            # Validate K-FAC factors dimensions
            self._validate_kfac_factors()

        except Exception as e:
            logger.error(f"Error during K-FAC factor extraction: {e}")
            logger.error(f"Debug info: {self._debug_info}")
            # Clear factors on error to prevent inconsistent state
            self.kfac_factors.clear()
            raise
        finally:
            # Remove hooks
            for hook in hooks:
                hook.remove()

    def _validate_kfac_factors(self):
        """Validate that K-FAC factors have consistent dimensions and provide detailed debugging."""
        if not self.kfac_factors:
            logger.warning("No K-FAC factors were extracted!")
            return False

        validation_passed = True

        # Group factors by module
        modules_factors = {}
        for key, factor in self.kfac_factors.items():
            module_id = key.split('_')[0]
            if module_id not in modules_factors:
                modules_factors[module_id] = {}
            factor_type = '_'.join(key.split('_')[1:])
            modules_factors[module_id][factor_type] = factor

        logger.debug(f"Validating K-FAC factors for {len(modules_factors)} modules:")

        for module_id, factors in modules_factors.items():
            logger.debug(f"  Module {module_id}:")

            # Check each factor
            for factor_type, factor in factors.items():
                logger.debug(f"    {factor_type}: {factor.shape}")

                # Validate basic properties
                if factor.dim() != 2:
                    logger.error(f"    ❌ {factor_type} has unexpected dimensions: {factor.shape}")
                    validation_passed = False

                if factor.shape[0] != factor.shape[1]:
                    logger.error(f"    ❌ {factor_type} is not square: {factor.shape}")
                    validation_passed = False

                if torch.isnan(factor).any():
                    logger.error(f"    ❌ {factor_type} contains NaN values")
                    validation_passed = False

                if torch.isinf(factor).any():
                    logger.error(f"    ❌ {factor_type} contains Inf values")
                    validation_passed = False

                # Check condition number for numerical stability
                try:
                    cond_num = torch.linalg.cond(factor)
                    if cond_num > 1e12:
                        logger.warning(f"    ⚠️ {factor_type} has high condition number: {cond_num:.2e}")
                except:
                    logger.warning(f"    ⚠️ Could not compute condition number for {factor_type}")

            # Check factor consistency within module
            if 'A_weight' in factors and 'G' in factors:
                A_weight = factors['A_weight']
                G = factors['G']

                # Get the actual linear module to check dimensions
                for name, module in self.model.named_modules():
                    if isinstance(module, nn.Linear) and str(id(module)) == module_id:
                        weight_shape = module.weight.shape  # [out_features, in_features]

                        # A_weight should match in_features, G should match out_features
                        if A_weight.shape[0] != weight_shape[1]:
                            logger.error(f"    ❌ A_weight dimension {A_weight.shape[0]} doesn't match weight in_features {weight_shape[1]}")
                            validation_passed = False

                        if G.shape[0] != weight_shape[0]:
                            logger.error(f"    ❌ G dimension {G.shape[0]} doesn't match weight out_features {weight_shape[0]}")
                            validation_passed = False

                        logger.debug(f"    ✅ Dimensions consistent with weight shape {weight_shape}")
                        break

        if validation_passed:
            logger.debug("✅ All K-FAC factors passed validation")
        else:
            logger.error("❌ K-FAC factor validation failed")

        return validation_passed

    def kfac_inverse_hvp(self, vector: torch.Tensor) -> torch.Tensor:
        """
        Compute (H + λI)^(-1) * vector using K-FAC approximation with robust dimension handling.

        Args:
            vector: Vector to multiply with inverse Hessian

        Returns:
            Result of (H + λI)^(-1) * vector
        """
        if not self.kfac_factors:
            logger.warning("K-FAC factors not computed, falling back to diagonal approximation")
            return vector / self.regularization_lambda

        result = torch.zeros_like(vector)
        param_idx = 0
        kfac_applied_count = 0
        fallback_count = 0

        logger.debug(f"Starting K-FAC inverse HVP with vector shape {vector.shape}")

        for name, param in self.model.named_parameters():
            if not param.requires_grad:
                continue

            param_size = param.numel()
            param_vector = vector[param_idx:param_idx + param_size].view(param.shape)

            # Check if this parameter has K-FAC factors
            module_id = None
            is_weight = False
            is_bias = False

            for module in self.model.modules():
                if isinstance(module, nn.Linear):
                    if param is module.weight:
                        module_id = str(id(module))
                        is_weight = True
                        break
                    elif param is module.bias:
                        module_id = str(id(module))
                        is_bias = True
                        break

            # Apply K-FAC inverse if factors are available
            if module_id and f"{module_id}_G" in self.kfac_factors:
                G = self.kfac_factors[f"{module_id}_G"]

                try:
                    logger.debug(f"Applying K-FAC to parameter {name} (module {module_id})")
                    logger.debug(f"  Parameter shape: {param_vector.shape}")
                    logger.debug(f"  G factor shape: {G.shape}")

                    # Add damping for numerical stability
                    G_damped = G + self.kfac_damping * torch.eye(G.shape[0], device=self.device, dtype=G.dtype)

                    # Use more stable inverse computation
                    try:
                        G_inv = torch.linalg.inv(G_damped)
                    except torch.linalg.LinAlgError:
                        # Fallback to pseudo-inverse for singular matrices
                        logger.warning(f"G matrix singular for {name}, using pseudo-inverse")
                        G_inv = torch.linalg.pinv(G_damped)

                    if is_weight and f"{module_id}_A_weight" in self.kfac_factors:
                        # Handle weight matrix: W = G^(-1) @ param_vector @ A^(-1)
                        A = self.kfac_factors[f"{module_id}_A_weight"]
                        logger.debug(f"  A_weight factor shape: {A.shape}")

                        A_damped = A + self.kfac_damping * torch.eye(A.shape[0], device=self.device, dtype=A.dtype)

                        try:
                            A_inv = torch.linalg.inv(A_damped)
                        except torch.linalg.LinAlgError:
                            logger.warning(f"A matrix singular for {name}, using pseudo-inverse")
                            A_inv = torch.linalg.pinv(A_damped)

                        # Check dimensions carefully
                        expected_G_dim = param_vector.shape[0]  # out_features
                        expected_A_dim = param_vector.shape[1]  # in_features

                        if G_inv.shape[0] == expected_G_dim and A_inv.shape[0] == expected_A_dim:
                            result_param = torch.mm(torch.mm(G_inv, param_vector), A_inv)
                            kfac_applied_count += 1
                            logger.debug(f"  ✅ K-FAC applied successfully to weight {name}")
                        else:
                            logger.warning(f"  ❌ Dimension mismatch for weight {name}:")
                            logger.warning(f"    Expected G: {expected_G_dim}, got: {G_inv.shape[0]}")
                            logger.warning(f"    Expected A: {expected_A_dim}, got: {A_inv.shape[0]}")
                            result_param = param_vector / self.regularization_lambda
                            fallback_count += 1

                    elif is_bias and f"{module_id}_A_bias" in self.kfac_factors:
                        # Handle bias vector: b = G^(-1) @ param_vector
                        expected_G_dim = param_vector.shape[0]  # out_features

                        if G_inv.shape[0] == expected_G_dim:
                            result_param = torch.mv(G_inv, param_vector)
                            kfac_applied_count += 1
                            logger.debug(f"  ✅ K-FAC applied successfully to bias {name}")
                        else:
                            logger.warning(f"  ❌ Dimension mismatch for bias {name}:")
                            logger.warning(f"    Expected G: {expected_G_dim}, got: {G_inv.shape[0]}")
                            result_param = param_vector / self.regularization_lambda
                            fallback_count += 1
                    else:
                        # No appropriate A factor found
                        logger.debug(f"  No appropriate A factor for {name}, using diagonal approximation")
                        result_param = param_vector / self.regularization_lambda
                        fallback_count += 1

                except Exception as e:
                    # Handle any other errors
                    logger.warning(f"K-FAC inverse failed for parameter {name}: {e}")
                    result_param = param_vector / self.regularization_lambda
                    fallback_count += 1
            else:
                # No K-FAC factors available
                result_param = param_vector / self.regularization_lambda
                fallback_count += 1

            result[param_idx:param_idx + param_size] = result_param.view(-1)
            param_idx += param_size

        logger.debug(f"K-FAC inverse HVP completed: {kfac_applied_count} K-FAC applications, {fallback_count} fallbacks")
        return result

    def compute_influence_score(self,
                              training_sample: Dict[str, torch.Tensor],
                              validation_batch: Dict[str, torch.Tensor],
                              loss_fn: callable) -> float:
        """
        Compute influence score using K-FAC approximation with memory management.

        Args:
            training_sample: Single training sample
            validation_batch: Validation batch
            loss_fn: Loss function

        Returns:
            Influence score
        """
        try:
            # Clear cache before computation
            torch.cuda.empty_cache()

            # Compute validation gradients if not cached
            if self.validation_gradients is None:
                self.compute_validation_gradients(validation_batch, loss_fn)

            # Extract K-FAC factors if using K-FAC (with memory management)
            if self.use_kfac:
                self._extract_kfac_factors_memory_efficient(validation_batch, loss_fn)

            # Compute training sample gradients with memory management
            training_gradients = self._compute_gradients_memory_efficient(training_sample, loss_fn)

            # Flatten gradients
            val_grad_flat = self.flatten_gradients(self.validation_gradients)
            train_grad_flat = self.flatten_gradients(training_gradients)

            # Clear intermediate gradients to save memory
            del training_gradients
            torch.cuda.empty_cache()

            # Apply K-FAC inverse or fallback to CG
            if self.use_kfac and self.kfac_factors:
                inverse_hvp = self.kfac_inverse_hvp(train_grad_flat)
            else:
                inverse_hvp = self._solve_inverse_hvp_memory_efficient(train_grad_flat, validation_batch, loss_fn)

            # Compute influence score
            influence_score = -torch.sum(val_grad_flat * inverse_hvp).item()

            # Clear intermediate tensors
            del val_grad_flat, train_grad_flat, inverse_hvp
            torch.cuda.empty_cache()

            print("成功计算影响函数fkac")
            return influence_score

        except torch.cuda.OutOfMemoryError as e:
            logger.warning(f"CUDA OOM in influence score computation: {e}")
            # Clear everything and return default score
            torch.cuda.empty_cache()
            return 0.0
        except Exception as e:
            logger.warning(f"Error in influence score computation: {e}")
            return 0.0

    def _compute_gradients_memory_efficient(self,
                                          batch: Dict[str, torch.Tensor],
                                          loss_fn: callable) -> Dict[str, torch.Tensor]:
        """
        Compute gradients with memory management.
        """
        # Clear any existing gradients
        self.model.zero_grad(set_to_none=True)

        try:
            # Forward pass with gradient checkpointing if available
            with torch.cuda.device(self.device):
                outputs = self.model(**batch)
                loss = loss_fn(outputs, batch)

                # Backward pass
                loss.backward()

                # Collect gradients immediately and detach
                gradients = {}
                for name, param in self.model.named_parameters():
                    if param.grad is not None:
                        gradients[name] = param.grad.clone().detach()

                # Clear gradients immediately after collection
                self.model.zero_grad(set_to_none=True)

                return gradients

        except torch.cuda.OutOfMemoryError:
            # Clear everything on OOM
            self.model.zero_grad(set_to_none=True)
            torch.cuda.empty_cache()
            raise

    def _extract_kfac_factors_memory_efficient(self, batch: Dict[str, torch.Tensor], loss_fn: callable):
        """
        Extract K-FAC factors with memory management and comprehensive dimension validation.
        """
        # Clear previous factors to save memory
        self.kfac_factors.clear()
        torch.cuda.empty_cache()

        # Use smaller batch size for factor extraction if needed
        batch_size = batch['input_ids'].size(0)
        original_batch = batch

        # Try progressively smaller batch sizes if extraction fails
        for attempt_batch_size in [min(batch_size, 1)]:  # Start with single sample
            try:
                if attempt_batch_size < batch_size:
                    logger.debug(f"Using reduced batch size {attempt_batch_size} for K-FAC factor extraction")
                    subset_batch = {}
                    for key, value in original_batch.items():
                        if isinstance(value, torch.Tensor):
                            subset_batch[key] = value[:attempt_batch_size]
                        else:
                            subset_batch[key] = value
                    batch = subset_batch
                else:
                    batch = original_batch

                logger.debug(f"Attempting K-FAC factor extraction with batch shapes:")
                for key, value in batch.items():
                    if isinstance(value, torch.Tensor):
                        logger.debug(f"  {key}: {value.shape}")

                # Extract factors with the improved method
                self.extract_kfac_factors(batch, loss_fn)

                # Validate extracted factors
                if not self.kfac_factors:
                    logger.warning("No K-FAC factors extracted")
                    continue

                # Run comprehensive validation
                validation_passed = self._validate_kfac_factors()

                if validation_passed:
                    logger.debug(f"✅ K-FAC factor extraction successful with batch size {attempt_batch_size}")
                    break
                else:
                    logger.warning(f"K-FAC factor validation failed with batch size {attempt_batch_size}")
                    self.kfac_factors.clear()

            except Exception as e:
                logger.warning(f"K-FAC factor extraction failed with batch size {attempt_batch_size}: {e}")
                self.kfac_factors.clear()

                # Log detailed error information
                import traceback
                logger.debug(f"Full traceback: {traceback.format_exc()}")

        # Final check
        if not self.kfac_factors:
            logger.warning("All K-FAC factor extraction attempts failed, will fall back to CG method")
        else:
            logger.debug(f"Successfully extracted {len(self.kfac_factors)} K-FAC factors")

        # Always clean up
        try:
            self.model.zero_grad(set_to_none=True)
            torch.cuda.empty_cache()
        except:
            pass

    def _solve_inverse_hvp_memory_efficient(self,
                                          vector: torch.Tensor,
                                          batch: Dict[str, torch.Tensor],
                                          loss_fn: callable,
                                          max_iterations: int = 50,  # Reduced iterations
                                          tolerance: float = 1e-4) -> torch.Tensor:  # Relaxed tolerance
        """
        Solve (H_θ + λI)^(-1) * vector using memory-efficient conjugate gradient.
        """
        # Use smaller batch for HVP computation if needed
        batch_size = batch['input_ids'].size(0)
        if batch_size > 2:
            # Use subset for HVP computation
            hvp_batch = {}
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    hvp_batch[key] = value[:1]  # Use only first sample
                else:
                    hvp_batch[key] = value
        else:
            hvp_batch = batch

        # Initialize solution
        x = torch.zeros_like(vector)
        r = vector.clone()
        p = r.clone()
        rsold = torch.sum(r * r)

        for i in range(max_iterations):
            try:
                # Clear cache before HVP computation
                torch.cuda.empty_cache()

                # Compute Hessian-vector product with regularization
                hvp = self._compute_hvp_memory_efficient(p, hvp_batch, loss_fn)
                hvp += self.regularization_lambda * p  # Add λI * p

                alpha = rsold / torch.sum(p * hvp)
                x += alpha * p
                r -= alpha * hvp
                rsnew = torch.sum(r * r)

                # Clear intermediate tensors
                del hvp
                torch.cuda.empty_cache()

                if torch.sqrt(rsnew) < tolerance:
                    logger.debug(f"CG converged in {i+1} iterations")
                    break

                beta = rsnew / rsold
                p = r + beta * p
                rsold = rsnew

            except torch.cuda.OutOfMemoryError:
                logger.warning(f"OOM in CG iteration {i}, breaking early")
                break

        return x

    def _compute_hvp_memory_efficient(self,
                                    vector: torch.Tensor,
                                    batch: Dict[str, torch.Tensor],
                                    loss_fn: callable) -> torch.Tensor:
        """
        Compute Hessian-vector product with memory management.
        """
        # Clear gradients
        self.model.zero_grad(set_to_none=True)

        try:
            # First compute gradients
            gradients = self._compute_gradients_memory_efficient(batch, loss_fn)
            flat_gradients = self.flatten_gradients(gradients)

            # Clear gradients dict to save memory
            del gradients
            torch.cuda.empty_cache()

            # Compute gradient-vector product
            gvp = torch.sum(flat_gradients * vector)

            # Clear flat_gradients
            del flat_gradients
            torch.cuda.empty_cache()

            # Compute second-order gradients (Hessian-vector product)
            self.model.zero_grad(set_to_none=True)
            gvp.backward()

            hvp_list = []
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    hvp_list.append(param.grad.view(-1).clone().detach())

            # Clear gradients immediately
            self.model.zero_grad(set_to_none=True)

            return torch.cat(hvp_list)

        except torch.cuda.OutOfMemoryError:
            # Clear everything on OOM
            self.model.zero_grad(set_to_none=True)
            torch.cuda.empty_cache()
            raise


class DistributedInfluenceFunctionCalculator:
    """
    Distributed influence function calculator that works with Ray worker groups.

    This calculator coordinates influence function computation across distributed workers
    without requiring direct access to the model on the driver process.
    """

    def __init__(self,
                 worker_group,
                 config: Optional[Dict] = None):
        """
        Initialize distributed influence function calculator.

        Args:
            worker_group: Ray worker group containing the distributed model
            config: Configuration dictionary with influence function parameters
        """
        self.worker_group = worker_group
        self.config = config or {}

        # Initialize influence function computation on workers
        self.worker_group.init_influence_functions(self.config)

        # Cache for validation gradients
        self._validation_gradients_cached = False

    def compute_validation_gradients(self, validation_data_proto):
        """
        Compute and cache validation gradients on distributed workers.

        Args:
            validation_data_proto: Validation data in DataProto format
        """
        # Send validation batch to workers and compute gradients
        self.worker_group.compute_validation_gradients(validation_data_proto)
        self._validation_gradients_cached = True

    def compute_influence_scores(self, training_data_proto):
        """
        Compute influence scores for training samples using distributed workers.

        Args:
            training_data_proto: Training data in DataProto format

        Returns:
            DataProto containing influence scores
        """
        if not self._validation_gradients_cached:
            raise ValueError("Validation gradients must be computed first")

        # Send training samples to workers and compute influence scores
        influence_output = self.worker_group.compute_influence_scores(training_data_proto)

        return influence_output

    def _aggregate_influence_scores(self, distributed_scores):
        """
        Aggregate influence scores from distributed workers.

        Args:
            distributed_scores: Scores from different workers

        Returns:
            Aggregated influence scores
        """
        # Simple aggregation - in practice, you might want more sophisticated methods
        if isinstance(distributed_scores, list) and len(distributed_scores) > 0:
            if isinstance(distributed_scores[0], list):
                # Flatten nested lists from multiple workers
                return [score for worker_scores in distributed_scores for score in worker_scores]
            else:
                return distributed_scores
        return []


def create_influence_calculator(model: nn.Module = None,
                              config: Optional[Dict] = None,
                              worker_group = None):
    """
    Factory function to create an influence function calculator.

    Args:
        model: Neural network model (for single-node computation)
        config: Configuration dictionary with influence function parameters
        worker_group: Ray worker group (for distributed computation)

    Returns:
        InfluenceFunctionCalculator or DistributedInfluenceFunctionCalculator instance
    """
    if config is None:
        config = {}

    # If worker_group is provided, use distributed calculator
    if worker_group is not None:
        return DistributedInfluenceFunctionCalculator(worker_group, config)

    # Otherwise, use single-node calculator
    if model is None:
        raise ValueError("Either model or worker_group must be provided")

    use_kfac = config.get('use_kfac', True)

    common_params = {
        'model': model,
        'regularization_lambda': config.get('regularization_lambda', 1e-3),
        'damping_factor': config.get('damping_factor', 1e-3),
        'use_kfac': use_kfac,
        'max_samples_per_batch': config.get('max_samples_per_batch', 32)
    }

    if use_kfac:
        # 为KFAC计算器添加特定参数
        common_params['kfac_damping'] = config.get('kfac_damping', 1e-3)
        return KFACInfluenceFunctionCalculator(**common_params)
    else:
        return InfluenceFunctionCalculator(**common_params)
