"""
Influence Function Computation Module

This module implements influence functions for curriculum learning, replacing perplexity-based
bandit updates with influence function calculations.

The influence function measures how a training sample z affects the loss on validation set D_r:
I_θ(D_r, z) = -∇_θ L(θ, D_r) (H_θ + λI)^(-1) ∇_θ L(θ, z)

Where:
- I_θ(D_r, z) is the influence function
- L(θ, D_r) is the loss function evaluated on validation set D_r  
- L(θ, z) is the loss function evaluated on training sample z
- θ represents the model parameters
- H_θ is the Hessian matrix of the loss with respect to parameters
- λ is a regularization parameter
- I is the identity matrix
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Union
import numpy as np
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)


class InfluenceFunctionCalculator:
    """
    Calculator for influence functions with K-FAC Hessian approximation.
    
    This class computes influence scores that measure how training samples
    affect validation performance, replacing perplexity-based scoring.
    """
    
    def __init__(self, 
                 model: nn.Module,
                 regularization_lambda: float = 1e-3,
                 damping_factor: float = 1e-3,
                 use_kfac: bool = True,
                 max_samples_per_batch: int = 32):
        """
        Initialize the influence function calculator.
        
        Args:
            model: The neural network model
            regularization_lambda: Regularization parameter λ for (H_θ + λI)^(-1)
            damping_factor: Damping factor for numerical stability
            use_kfac: Whether to use K-FAC approximation for Hessian
            max_samples_per_batch: Maximum samples to process in one batch
        """
        self.model = model
        self.regularization_lambda = regularization_lambda
        self.damping_factor = damping_factor
        self.use_kfac = use_kfac
        self.max_samples_per_batch = max_samples_per_batch
        
        # Cache for gradients and Hessian information
        self.validation_gradients = None
        self.hessian_info = None
        self.device = next(model.parameters()).device
        
    def compute_gradients(self,
                         batch: Dict[str, torch.Tensor],
                         loss_fn: callable) -> Dict[str, torch.Tensor]:
        """
        Compute gradients of the loss with respect to model parameters.

        Args:
            batch: Input batch containing input_ids, attention_mask, etc.
            loss_fn: Loss function that takes model output and returns loss

        Returns:
            Dictionary mapping parameter names to gradient tensors
        """
        # Clear any existing gradients with memory efficiency
        self.model.zero_grad(set_to_none=True)

        try:
            # Forward pass
            outputs = self.model(**batch)
            loss = loss_fn(outputs, batch)

            # Backward pass to compute gradients
            loss.backward()

            # Collect gradients and detach immediately
            gradients = {}
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    gradients[name] = param.grad.clone().detach()

            # Clear gradients immediately after collection
            self.model.zero_grad(set_to_none=True)

            return gradients

        except torch.cuda.OutOfMemoryError:
            # Clear everything on OOM
            self.model.zero_grad(set_to_none=True)
            torch.cuda.empty_cache()
            raise
    
    def compute_validation_gradients(self, 
                                   validation_batch: Dict[str, torch.Tensor],
                                   loss_fn: callable) -> None:
        """
        Compute and cache gradients on validation set D_r.
        
        Args:
            validation_batch: Validation data batch
            loss_fn: Loss function
        """
        self.validation_gradients = self.compute_gradients(validation_batch, loss_fn)
        logger.debug(f"Computed validation gradients for {len(self.validation_gradients)} parameters")
    
    def flatten_gradients(self, gradients: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Flatten gradient dictionary into a single vector.
        
        Args:
            gradients: Dictionary of parameter gradients
            
        Returns:
            Flattened gradient vector
        """
        grad_list = []
        for name in sorted(gradients.keys()):
            grad_list.append(gradients[name].view(-1))
        return torch.cat(grad_list)
    
    def compute_hvp(self,
                   vector: torch.Tensor,
                   batch: Dict[str, torch.Tensor],
                   loss_fn: callable) -> torch.Tensor:
        """
        Compute Hessian-vector product H_θ * vector efficiently with memory management.

        Uses the identity: H_θ * v = ∇_θ (∇_θ L(θ) · v)

        Args:
            vector: Vector to multiply with Hessian
            batch: Input batch for computing Hessian
            loss_fn: Loss function

        Returns:
            Hessian-vector product
        """
        try:
            # First compute gradients
            gradients = self.compute_gradients(batch, loss_fn)
            flat_gradients = self.flatten_gradients(gradients)

            # Clear gradients dict to save memory
            del gradients
            torch.cuda.empty_cache()

            # Compute gradient-vector product
            gvp = torch.sum(flat_gradients * vector)

            # Clear flat_gradients
            del flat_gradients
            torch.cuda.empty_cache()

            # Compute second-order gradients (Hessian-vector product)
            self.model.zero_grad(set_to_none=True)
            gvp.backward()

            hvp_list = []
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    hvp_list.append(param.grad.view(-1).clone().detach())

            # Clear gradients immediately
            self.model.zero_grad(set_to_none=True)

            return torch.cat(hvp_list)

        except torch.cuda.OutOfMemoryError:
            # Clear everything on OOM
            self.model.zero_grad(set_to_none=True)
            torch.cuda.empty_cache()
            raise
    
    def solve_inverse_hvp(self,
                         vector: torch.Tensor,
                         batch: Dict[str, torch.Tensor],
                         loss_fn: callable,
                         max_iterations: int = 50,  # Reduced default iterations
                         tolerance: float = 1e-4) -> torch.Tensor:  # Relaxed tolerance
        """
        Solve (H_θ + λI)^(-1) * vector using memory-efficient conjugate gradient method.

        Args:
            vector: Vector to solve for
            batch: Batch for Hessian computation
            loss_fn: Loss function
            max_iterations: Maximum CG iterations
            tolerance: Convergence tolerance

        Returns:
            Solution to (H_θ + λI)^(-1) * vector
        """
        # Use smaller batch for HVP computation if needed
        batch_size = batch['input_ids'].size(0) if 'input_ids' in batch else 1
        if batch_size > 2:
            # Use subset for HVP computation to save memory
            hvp_batch = {}
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    hvp_batch[key] = value[:1]  # Use only first sample
                else:
                    hvp_batch[key] = value
        else:
            hvp_batch = batch

        # Initialize solution
        x = torch.zeros_like(vector)
        r = vector.clone()
        p = r.clone()
        rsold = torch.sum(r * r)

        for i in range(max_iterations):
            try:
                # Clear cache before HVP computation
                torch.cuda.empty_cache()

                # Compute Hessian-vector product with regularization
                hvp = self.compute_hvp(p, hvp_batch, loss_fn)
                hvp += self.regularization_lambda * p  # Add λI * p

                alpha = rsold / torch.sum(p * hvp)
                x += alpha * p
                r -= alpha * hvp
                rsnew = torch.sum(r * r)

                # Clear intermediate tensors
                del hvp
                torch.cuda.empty_cache()

                if torch.sqrt(rsnew) < tolerance:
                    logger.debug(f"CG converged in {i+1} iterations")
                    break

                beta = rsnew / rsold
                p = r + beta * p
                rsold = rsnew

            except torch.cuda.OutOfMemoryError:
                logger.warning(f"OOM in CG iteration {i}, breaking early")
                break

        return x
    
    def compute_influence_score(self,
                              training_sample: Dict[str, torch.Tensor],
                              validation_batch: Dict[str, torch.Tensor],
                              loss_fn: callable) -> float:
        """
        Compute influence score for a single training sample with memory management.

        Args:
            training_sample: Single training sample
            validation_batch: Validation batch for computing validation gradients
            loss_fn: Loss function

        Returns:
            Influence score (scalar)
        """
        try:
            # Clear cache before computation
            torch.cuda.empty_cache()

            # Compute validation gradients if not cached
            if self.validation_gradients is None:
                self.compute_validation_gradients(validation_batch, loss_fn)

            # Compute training sample gradients
            training_gradients = self.compute_gradients(training_sample, loss_fn)

            # Flatten gradients
            val_grad_flat = self.flatten_gradients(self.validation_gradients)
            train_grad_flat = self.flatten_gradients(training_gradients)

            # Clear intermediate gradients to save memory
            del training_gradients
            torch.cuda.empty_cache()

            # Solve (H_θ + λI)^(-1) * ∇_θ L(θ, z)
            inverse_hvp = self.solve_inverse_hvp(train_grad_flat, validation_batch, loss_fn)

            # Compute influence: -∇_θ L(θ, D_r) · (H_θ + λI)^(-1) ∇_θ L(θ, z)
            influence_score = -torch.sum(val_grad_flat * inverse_hvp).item()

            # Clear intermediate tensors
            del val_grad_flat, train_grad_flat, inverse_hvp
            torch.cuda.empty_cache()

            print("成功计算影响函数")
            return influence_score

        except torch.cuda.OutOfMemoryError as e:
            logger.warning(f"CUDA OOM in influence score computation: {e}")
            # Clear everything and return default score
            torch.cuda.empty_cache()
            return 0.0
        except Exception as e:
            logger.warning(f"Error in influence score computation: {e}")
            return 0.0

    def compute_influence_scores_batch(self,
                                     training_samples: List[Dict[str, torch.Tensor]],
                                     validation_batch: Dict[str, torch.Tensor],
                                     loss_fn: callable) -> List[float]:
        """
        Compute influence scores for a batch of training samples.

        Args:
            training_samples: List of training samples
            validation_batch: Validation batch
            loss_fn: Loss function

        Returns:
            List of influence scores
        """
        influence_scores = []

        # Process in smaller batches to manage memory
        for i in range(0, len(training_samples), self.max_samples_per_batch):
            batch_samples = training_samples[i:i + self.max_samples_per_batch]

            for sample in batch_samples:
                try:
                    score = self.compute_influence_score(sample, validation_batch, loss_fn)
                    influence_scores.append(score)
                except Exception as e:
                    logger.warning(f"Failed to compute influence score for sample {i}: {e}")
                    influence_scores.append(0.0)  # Default score on failure

        return influence_scores


class KFACInfluenceFunctionCalculator(InfluenceFunctionCalculator):
    """
    Influence function calculator with K-FAC (Kronecker-Factored Approximate Curvature)
    Hessian approximation for improved computational efficiency.
    """

    def __init__(self, *args, **kwargs):
        # 提取KFACInfluenceFunctionCalculator特有的参数
        self.kfac_damping = kwargs.pop('kfac_damping', 1e-3)
        # 调用父类构造函数，不会再传递kfac_damping参数
        super().__init__(*args, **kwargs)
        self.kfac_factors = {}

    def extract_kfac_factors(self, batch: Dict[str, torch.Tensor], loss_fn: callable):
        """
        Extract K-FAC factors for Kronecker product approximation of the Hessian.

        For linear layers: H ≈ A ⊗ G where A is input covariance, G is gradient covariance

        Args:
            batch: Input batch
            loss_fn: Loss function
        """
        self.kfac_factors = {}

        # Register hooks to capture activations and gradients
        def forward_hook(module, input, output):
            if isinstance(module, nn.Linear):
                # Store input activations for A factor
                activations = input[0].detach()

                # Reshape to 2D: [batch_size * seq_len, input_dim]
                if len(activations.shape) > 2:
                    batch_size, seq_len = activations.shape[:2]
                    input_dim = activations.shape[-1]
                    activations = activations.view(batch_size * seq_len, input_dim)

                # Store original activations without bias for weight computation
                self.kfac_factors[f"{id(module)}_A_weight"] = torch.mm(activations.t(), activations) / activations.shape[0]

                # Store activations with bias term for bias computation if bias exists
                if module.bias is not None:
                    ones = torch.ones(activations.shape[0], 1, device=activations.device, dtype=activations.dtype)
                    activations_with_bias = torch.cat([activations, ones], dim=1)
                    self.kfac_factors[f"{id(module)}_A_bias"] = torch.mm(activations_with_bias.t(), activations_with_bias) / activations_with_bias.shape[0]

        def backward_hook(module, grad_input, grad_output):
            if isinstance(module, nn.Linear):
                # Store output gradients for G factor
                grad_out = grad_output[0].detach()

                # Reshape to 2D: [batch_size * seq_len, output_dim]
                if len(grad_out.shape) > 2:
                    batch_size, seq_len = grad_out.shape[:2]
                    output_dim = grad_out.shape[-1]
                    grad_out = grad_out.view(batch_size * seq_len, output_dim)

                self.kfac_factors[f"{id(module)}_G"] = torch.mm(grad_out.t(), grad_out) / grad_out.shape[0]

        # Register hooks
        hooks = []
        for module in self.model.modules():
            if isinstance(module, nn.Linear):
                hooks.append(module.register_forward_hook(forward_hook))
                hooks.append(module.register_backward_hook(backward_hook))

        try:
            # Forward and backward pass to collect factors
            outputs = self.model(**batch)
            loss = loss_fn(outputs, batch)
            loss.backward()

            # Validate K-FAC factors dimensions
            self._validate_kfac_factors()

        finally:
            # Remove hooks
            for hook in hooks:
                hook.remove()

    def _validate_kfac_factors(self):
        """Validate that K-FAC factors have consistent dimensions."""
        for key, factor in self.kfac_factors.items():
            if factor.dim() != 2:
                logger.warning(f"K-FAC factor {key} has unexpected dimensions: {factor.shape}")
            if factor.shape[0] != factor.shape[1]:
                logger.warning(f"K-FAC factor {key} is not square: {factor.shape}")
            if torch.isnan(factor).any() or torch.isinf(factor).any():
                logger.warning(f"K-FAC factor {key} contains NaN or Inf values")

        # Log factor dimensions for debugging
        logger.debug("K-FAC factors extracted:")
        for key, factor in self.kfac_factors.items():
            logger.debug(f"  {key}: {factor.shape}")

    def kfac_inverse_hvp(self, vector: torch.Tensor) -> torch.Tensor:
        """
        Compute (H + λI)^(-1) * vector using K-FAC approximation.

        Args:
            vector: Vector to multiply with inverse Hessian

        Returns:
            Result of (H + λI)^(-1) * vector
        """
        if not self.kfac_factors:
            logger.warning("K-FAC factors not computed, falling back to standard method")
            return vector / self.regularization_lambda

        result = torch.zeros_like(vector)
        param_idx = 0

        for name, param in self.model.named_parameters():
            if not param.requires_grad:
                continue

            param_size = param.numel()
            param_vector = vector[param_idx:param_idx + param_size].view(param.shape)

            # Check if this parameter has K-FAC factors
            module_id = None
            is_weight = False
            is_bias = False

            for module in self.model.modules():
                if isinstance(module, nn.Linear):
                    if param is module.weight:
                        module_id = id(module)
                        is_weight = True
                        break
                    elif param is module.bias:
                        module_id = id(module)
                        is_bias = True
                        break

            # Apply K-FAC inverse if factors are available
            if module_id and f"{module_id}_G" in self.kfac_factors:
                G = self.kfac_factors[f"{module_id}_G"]

                try:
                    # Add damping for numerical stability
                    G_damped = G + self.kfac_damping * torch.eye(G.shape[0], device=self.device, dtype=G.dtype)
                    G_inv = torch.inverse(G_damped)

                    if is_weight and f"{module_id}_A_weight" in self.kfac_factors:
                        # Handle weight matrix: W = G^(-1) @ param_vector @ A^(-1)
                        A = self.kfac_factors[f"{module_id}_A_weight"]
                        A_damped = A + self.kfac_damping * torch.eye(A.shape[0], device=self.device, dtype=A.dtype)
                        A_inv = torch.inverse(A_damped)

                        # Ensure dimensions match for matrix multiplication
                        if param_vector.shape[0] == G_inv.shape[1] and param_vector.shape[1] == A_inv.shape[0]:
                            result_param = torch.mm(torch.mm(G_inv, param_vector), A_inv)
                        else:
                            # Fallback if dimensions don't match
                            result_param = param_vector / self.regularization_lambda

                    elif is_bias and f"{module_id}_A_bias" in self.kfac_factors:
                        # Handle bias vector: b = G^(-1) @ param_vector
                        if param_vector.shape[0] == G_inv.shape[1]:
                            result_param = torch.mv(G_inv, param_vector)
                        else:
                            # Fallback if dimensions don't match
                            result_param = param_vector / self.regularization_lambda
                    else:
                        # Fallback to diagonal approximation
                        result_param = param_vector / self.regularization_lambda

                except (RuntimeError, torch.linalg.LinAlgError) as e:
                    # Handle singular matrix or other linear algebra errors
                    logger.warning(f"K-FAC inverse failed for parameter {name}: {e}")
                    result_param = param_vector / self.regularization_lambda
            else:
                # Fallback to diagonal approximation
                result_param = param_vector / self.regularization_lambda

            result[param_idx:param_idx + param_size] = result_param.view(-1)
            param_idx += param_size

        return result

    def compute_influence_score(self,
                              training_sample: Dict[str, torch.Tensor],
                              validation_batch: Dict[str, torch.Tensor],
                              loss_fn: callable) -> float:
        """
        Compute influence score using K-FAC approximation with memory management.

        Args:
            training_sample: Single training sample
            validation_batch: Validation batch
            loss_fn: Loss function

        Returns:
            Influence score
        """
        try:
            # Clear cache before computation
            torch.cuda.empty_cache()

            # Compute validation gradients if not cached
            if self.validation_gradients is None:
                self.compute_validation_gradients(validation_batch, loss_fn)

            # Extract K-FAC factors if using K-FAC (with memory management)
            if self.use_kfac:
                self._extract_kfac_factors_memory_efficient(validation_batch, loss_fn)

            # Compute training sample gradients with memory management
            training_gradients = self._compute_gradients_memory_efficient(training_sample, loss_fn)

            # Flatten gradients
            val_grad_flat = self.flatten_gradients(self.validation_gradients)
            train_grad_flat = self.flatten_gradients(training_gradients)

            # Clear intermediate gradients to save memory
            del training_gradients
            torch.cuda.empty_cache()

            # Apply K-FAC inverse or fallback to CG
            if self.use_kfac and self.kfac_factors:
                inverse_hvp = self.kfac_inverse_hvp(train_grad_flat)
            else:
                inverse_hvp = self._solve_inverse_hvp_memory_efficient(train_grad_flat, validation_batch, loss_fn)

            # Compute influence score
            influence_score = -torch.sum(val_grad_flat * inverse_hvp).item()

            # Clear intermediate tensors
            del val_grad_flat, train_grad_flat, inverse_hvp
            torch.cuda.empty_cache()

            print("成功计算影响函数fkac")
            return influence_score

        except torch.cuda.OutOfMemoryError as e:
            logger.warning(f"CUDA OOM in influence score computation: {e}")
            # Clear everything and return default score
            torch.cuda.empty_cache()
            return 0.0
        except Exception as e:
            logger.warning(f"Error in influence score computation: {e}")
            return 0.0

    def _compute_gradients_memory_efficient(self,
                                          batch: Dict[str, torch.Tensor],
                                          loss_fn: callable) -> Dict[str, torch.Tensor]:
        """
        Compute gradients with memory management.
        """
        # Clear any existing gradients
        self.model.zero_grad(set_to_none=True)

        try:
            # Forward pass with gradient checkpointing if available
            with torch.cuda.device(self.device):
                outputs = self.model(**batch)
                loss = loss_fn(outputs, batch)

                # Backward pass
                loss.backward()

                # Collect gradients immediately and detach
                gradients = {}
                for name, param in self.model.named_parameters():
                    if param.grad is not None:
                        gradients[name] = param.grad.clone().detach()

                # Clear gradients immediately after collection
                self.model.zero_grad(set_to_none=True)

                return gradients

        except torch.cuda.OutOfMemoryError:
            # Clear everything on OOM
            self.model.zero_grad(set_to_none=True)
            torch.cuda.empty_cache()
            raise

    def _extract_kfac_factors_memory_efficient(self, batch: Dict[str, torch.Tensor], loss_fn: callable):
        """
        Extract K-FAC factors with memory management and dimension validation.
        """
        # Clear previous factors to save memory
        self.kfac_factors.clear()
        torch.cuda.empty_cache()

        # Use smaller batch size for factor extraction if needed
        batch_size = batch['input_ids'].size(0)
        if batch_size > 2:  # Use even smaller batch for factor extraction
            # Take a subset of the batch
            subset_batch = {}
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    subset_batch[key] = value[:1]  # Use only first sample for stability
                else:
                    subset_batch[key] = value
            batch = subset_batch

        # Extract factors with the original method but with cleanup
        try:
            self.extract_kfac_factors(batch, loss_fn)

            # Validate extracted factors
            if not self.kfac_factors:
                logger.warning("No K-FAC factors extracted, falling back to CG method")

        except Exception as e:
            logger.warning(f"K-FAC factor extraction failed: {e}, falling back to CG method")
            self.kfac_factors.clear()
        finally:
            # Clear gradients after factor extraction
            self.model.zero_grad(set_to_none=True)
            torch.cuda.empty_cache()

    def _solve_inverse_hvp_memory_efficient(self,
                                          vector: torch.Tensor,
                                          batch: Dict[str, torch.Tensor],
                                          loss_fn: callable,
                                          max_iterations: int = 50,  # Reduced iterations
                                          tolerance: float = 1e-4) -> torch.Tensor:  # Relaxed tolerance
        """
        Solve (H_θ + λI)^(-1) * vector using memory-efficient conjugate gradient.
        """
        # Use smaller batch for HVP computation if needed
        batch_size = batch['input_ids'].size(0)
        if batch_size > 2:
            # Use subset for HVP computation
            hvp_batch = {}
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    hvp_batch[key] = value[:1]  # Use only first sample
                else:
                    hvp_batch[key] = value
        else:
            hvp_batch = batch

        # Initialize solution
        x = torch.zeros_like(vector)
        r = vector.clone()
        p = r.clone()
        rsold = torch.sum(r * r)

        for i in range(max_iterations):
            try:
                # Clear cache before HVP computation
                torch.cuda.empty_cache()

                # Compute Hessian-vector product with regularization
                hvp = self._compute_hvp_memory_efficient(p, hvp_batch, loss_fn)
                hvp += self.regularization_lambda * p  # Add λI * p

                alpha = rsold / torch.sum(p * hvp)
                x += alpha * p
                r -= alpha * hvp
                rsnew = torch.sum(r * r)

                # Clear intermediate tensors
                del hvp
                torch.cuda.empty_cache()

                if torch.sqrt(rsnew) < tolerance:
                    logger.debug(f"CG converged in {i+1} iterations")
                    break

                beta = rsnew / rsold
                p = r + beta * p
                rsold = rsnew

            except torch.cuda.OutOfMemoryError:
                logger.warning(f"OOM in CG iteration {i}, breaking early")
                break

        return x

    def _compute_hvp_memory_efficient(self,
                                    vector: torch.Tensor,
                                    batch: Dict[str, torch.Tensor],
                                    loss_fn: callable) -> torch.Tensor:
        """
        Compute Hessian-vector product with memory management.
        """
        # Clear gradients
        self.model.zero_grad(set_to_none=True)

        try:
            # First compute gradients
            gradients = self._compute_gradients_memory_efficient(batch, loss_fn)
            flat_gradients = self.flatten_gradients(gradients)

            # Clear gradients dict to save memory
            del gradients
            torch.cuda.empty_cache()

            # Compute gradient-vector product
            gvp = torch.sum(flat_gradients * vector)

            # Clear flat_gradients
            del flat_gradients
            torch.cuda.empty_cache()

            # Compute second-order gradients (Hessian-vector product)
            self.model.zero_grad(set_to_none=True)
            gvp.backward()

            hvp_list = []
            for name, param in self.model.named_parameters():
                if param.grad is not None:
                    hvp_list.append(param.grad.view(-1).clone().detach())

            # Clear gradients immediately
            self.model.zero_grad(set_to_none=True)

            return torch.cat(hvp_list)

        except torch.cuda.OutOfMemoryError:
            # Clear everything on OOM
            self.model.zero_grad(set_to_none=True)
            torch.cuda.empty_cache()
            raise


class DistributedInfluenceFunctionCalculator:
    """
    Distributed influence function calculator that works with Ray worker groups.

    This calculator coordinates influence function computation across distributed workers
    without requiring direct access to the model on the driver process.
    """

    def __init__(self,
                 worker_group,
                 config: Optional[Dict] = None):
        """
        Initialize distributed influence function calculator.

        Args:
            worker_group: Ray worker group containing the distributed model
            config: Configuration dictionary with influence function parameters
        """
        self.worker_group = worker_group
        self.config = config or {}

        # Initialize influence function computation on workers
        self.worker_group.init_influence_functions(self.config)

        # Cache for validation gradients
        self._validation_gradients_cached = False

    def compute_validation_gradients(self, validation_data_proto):
        """
        Compute and cache validation gradients on distributed workers.

        Args:
            validation_data_proto: Validation data in DataProto format
        """
        # Send validation batch to workers and compute gradients
        self.worker_group.compute_validation_gradients(validation_data_proto)
        self._validation_gradients_cached = True

    def compute_influence_scores(self, training_data_proto):
        """
        Compute influence scores for training samples using distributed workers.

        Args:
            training_data_proto: Training data in DataProto format

        Returns:
            DataProto containing influence scores
        """
        if not self._validation_gradients_cached:
            raise ValueError("Validation gradients must be computed first")

        # Send training samples to workers and compute influence scores
        influence_output = self.worker_group.compute_influence_scores(training_data_proto)

        return influence_output

    def _aggregate_influence_scores(self, distributed_scores):
        """
        Aggregate influence scores from distributed workers.

        Args:
            distributed_scores: Scores from different workers

        Returns:
            Aggregated influence scores
        """
        # Simple aggregation - in practice, you might want more sophisticated methods
        if isinstance(distributed_scores, list) and len(distributed_scores) > 0:
            if isinstance(distributed_scores[0], list):
                # Flatten nested lists from multiple workers
                return [score for worker_scores in distributed_scores for score in worker_scores]
            else:
                return distributed_scores
        return []


def create_influence_calculator(model: nn.Module = None,
                              config: Optional[Dict] = None,
                              worker_group = None):
    """
    Factory function to create an influence function calculator.

    Args:
        model: Neural network model (for single-node computation)
        config: Configuration dictionary with influence function parameters
        worker_group: Ray worker group (for distributed computation)

    Returns:
        InfluenceFunctionCalculator or DistributedInfluenceFunctionCalculator instance
    """
    if config is None:
        config = {}

    # If worker_group is provided, use distributed calculator
    if worker_group is not None:
        return DistributedInfluenceFunctionCalculator(worker_group, config)

    # Otherwise, use single-node calculator
    if model is None:
        raise ValueError("Either model or worker_group must be provided")

    use_kfac = config.get('use_kfac', True)

    common_params = {
        'model': model,
        'regularization_lambda': config.get('regularization_lambda', 1e-3),
        'damping_factor': config.get('damping_factor', 1e-3),
        'use_kfac': use_kfac,
        'max_samples_per_batch': config.get('max_samples_per_batch', 32)
    }

    if use_kfac:
        # 为KFAC计算器添加特定参数
        common_params['kfac_damping'] = config.get('kfac_damping', 1e-3)
        return KFACInfluenceFunctionCalculator(**common_params)
    else:
        return InfluenceFunctionCalculator(**common_params)
