"""
K-FAC (Kronecker-Factored Approximate Curvature) Implementation

This module provides utilities for K-FAC approximation of the Fisher Information Matrix
and Hessian, making second-order optimization methods computationally tractable.

K-FAC approximates the Fisher Information Matrix (or Hessian) as a block-diagonal matrix
where each block corresponds to a layer, and each block is approximated as a Kronecker product.

For a linear layer with weight W and input a, output s = Wa + b:
F_W ≈ E[aa^T] ⊗ E[∇_s L ∇_s L^T] = A ⊗ G

Where:
- A = E[aa^T] is the input covariance matrix
- G = E[∇_s L ∇_s L^T] is the output gradient covariance matrix
- ⊗ denotes the Kronecker product
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class KFACLayer:
    """
    K-FAC statistics for a single layer.
    """
    
    def __init__(self, layer: nn.Module, damping: float = 1e-3):
        """
        Initialize K-FAC layer statistics.
        
        Args:
            layer: Neural network layer (Linear, Conv2d, etc.)
            damping: Damping factor for numerical stability
        """
        self.layer = layer
        self.damping = damping
        self.A = None  # Input covariance matrix
        self.G = None  # Output gradient covariance matrix
        self.num_updates = 0
        
    def update_A(self, input_tensor: torch.Tensor):
        """
        Update input covariance matrix A.
        
        Args:
            input_tensor: Input activations to the layer
        """
        if isinstance(self.layer, nn.Linear):
            # For linear layers, flatten batch dimension
            if len(input_tensor.shape) > 2:
                input_tensor = input_tensor.view(-1, input_tensor.shape[-1])
            
            # Add bias term if layer has bias
            if self.layer.bias is not None:
                ones = torch.ones(input_tensor.shape[0], 1, device=input_tensor.device)
                input_tensor = torch.cat([input_tensor, ones], dim=1)
            
            # Compute covariance: A = (1/n) * X^T * X
            batch_size = input_tensor.shape[0]
            A_new = torch.mm(input_tensor.t(), input_tensor) / batch_size
            
        elif isinstance(self.layer, (nn.Conv1d, nn.Conv2d, nn.Conv3d)):
            # For convolutional layers, use im2col transformation
            A_new = self._compute_conv_input_cov(input_tensor)
        else:
            logger.warning(f"K-FAC not implemented for layer type {type(self.layer)}")
            return
        
        # Exponential moving average update
        if self.A is None:
            self.A = A_new
        else:
            momentum = 0.95  # Momentum for moving average
            self.A = momentum * self.A + (1 - momentum) * A_new
    
    def update_G(self, grad_output: torch.Tensor):
        """
        Update output gradient covariance matrix G.
        
        Args:
            grad_output: Gradient with respect to layer output
        """
        if isinstance(self.layer, nn.Linear):
            # For linear layers
            if len(grad_output.shape) > 2:
                grad_output = grad_output.view(-1, grad_output.shape[-1])
            
            batch_size = grad_output.shape[0]
            G_new = torch.mm(grad_output.t(), grad_output) / batch_size
            
        elif isinstance(self.layer, (nn.Conv1d, nn.Conv2d, nn.Conv3d)):
            # For convolutional layers
            G_new = self._compute_conv_grad_cov(grad_output)
        else:
            logger.warning(f"K-FAC not implemented for layer type {type(self.layer)}")
            return
        
        # Exponential moving average update
        if self.G is None:
            self.G = G_new
        else:
            momentum = 0.95
            self.G = momentum * self.G + (1 - momentum) * G_new
    
    def _compute_conv_input_cov(self, input_tensor: torch.Tensor) -> torch.Tensor:
        """
        Compute input covariance for convolutional layers using im2col.
        
        Args:
            input_tensor: Input tensor to conv layer
            
        Returns:
            Input covariance matrix
        """
        # Simplified implementation - in practice, would use proper im2col
        # For now, treat as spatial average
        if isinstance(self.layer, nn.Conv2d):
            # Average over spatial dimensions
            batch_size, channels, height, width = input_tensor.shape
            input_flat = input_tensor.view(batch_size, channels, -1).mean(dim=2)
            return torch.mm(input_flat.t(), input_flat) / batch_size
        else:
            # Fallback for other conv types
            input_flat = input_tensor.view(input_tensor.shape[0], -1)
            return torch.mm(input_flat.t(), input_flat) / input_tensor.shape[0]
    
    def _compute_conv_grad_cov(self, grad_output: torch.Tensor) -> torch.Tensor:
        """
        Compute gradient covariance for convolutional layers.
        
        Args:
            grad_output: Gradient tensor
            
        Returns:
            Gradient covariance matrix
        """
        if isinstance(self.layer, nn.Conv2d):
            # Average over spatial dimensions
            batch_size, channels, height, width = grad_output.shape
            grad_flat = grad_output.view(batch_size, channels, -1).mean(dim=2)
            return torch.mm(grad_flat.t(), grad_flat) / batch_size
        else:
            # Fallback
            grad_flat = grad_output.view(grad_output.shape[0], -1)
            return torch.mm(grad_flat.t(), grad_flat) / grad_output.shape[0]
    
    def get_inverse_factors(self) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get inverse of K-FAC factors with damping.
        
        Returns:
            Tuple of (A_inv, G_inv)
        """
        if self.A is None or self.G is None:
            raise ValueError("K-FAC factors not computed yet")
        
        # Add damping for numerical stability
        A_damped = self.A + self.damping * torch.eye(self.A.shape[0], device=self.A.device)
        G_damped = self.G + self.damping * torch.eye(self.G.shape[0], device=self.G.device)
        
        # Compute inverses
        try:
            A_inv = torch.inverse(A_damped)
            G_inv = torch.inverse(G_damped)
        except RuntimeError as e:
            logger.warning(f"Failed to invert K-FAC factors: {e}. Using pseudo-inverse.")
            A_inv = torch.pinverse(A_damped)
            G_inv = torch.pinverse(G_damped)
        
        return A_inv, G_inv
    
    def apply_inverse(self, grad_tensor: torch.Tensor) -> torch.Tensor:
        """
        Apply K-FAC inverse to a gradient tensor.
        
        For weight gradients: (A^-1 ⊗ G^-1) vec(∇W) = vec(G^-1 ∇W A^-1)
        For bias gradients: G^-1 ∇b
        
        Args:
            grad_tensor: Gradient tensor to transform
            
        Returns:
            Transformed gradient tensor
        """
        if self.A is None or self.G is None:
            logger.warning("K-FAC factors not available, returning original gradient")
            return grad_tensor
        
        try:
            A_inv, G_inv = self.get_inverse_factors()
            
            if grad_tensor.dim() == 2:  # Weight matrix
                # Apply: G^-1 * grad * A^-1
                result = torch.mm(torch.mm(G_inv, grad_tensor), A_inv)
            elif grad_tensor.dim() == 1:  # Bias vector
                # Apply: G^-1 * grad
                result = torch.mv(G_inv, grad_tensor)
            else:
                logger.warning(f"Unsupported gradient tensor shape: {grad_tensor.shape}")
                result = grad_tensor
                
            return result
            
        except Exception as e:
            logger.warning(f"Failed to apply K-FAC inverse: {e}")
            return grad_tensor


class KFACOptimizer:
    """
    K-FAC optimizer that maintains and updates K-FAC statistics for all layers.
    """
    
    def __init__(self, model: nn.Module, damping: float = 1e-3, update_freq: int = 10):
        """
        Initialize K-FAC optimizer.
        
        Args:
            model: Neural network model
            damping: Damping factor for K-FAC matrices
            update_freq: Frequency of K-FAC factor updates
        """
        self.model = model
        self.damping = damping
        self.update_freq = update_freq
        self.step_count = 0
        
        # Initialize K-FAC layers
        self.kfac_layers = {}
        self._register_layers()
        
        # Hook handles for cleanup
        self.hooks = []
        
    def _register_layers(self):
        """Register K-FAC statistics for supported layers."""
        for name, module in self.model.named_modules():
            if isinstance(module, (nn.Linear, nn.Conv1d, nn.Conv2d, nn.Conv3d)):
                self.kfac_layers[name] = KFACLayer(module, self.damping)
                logger.debug(f"Registered K-FAC layer: {name}")
    
    def register_hooks(self):
        """Register forward and backward hooks to collect K-FAC statistics."""
        def make_forward_hook(layer_name):
            def forward_hook(module, input, output):
                if layer_name in self.kfac_layers:
                    self.kfac_layers[layer_name].update_A(input[0])
            return forward_hook
        
        def make_backward_hook(layer_name):
            def backward_hook(module, grad_input, grad_output):
                if layer_name in self.kfac_layers and grad_output[0] is not None:
                    self.kfac_layers[layer_name].update_G(grad_output[0])
            return backward_hook
        
        # Register hooks for all K-FAC layers
        for name, module in self.model.named_modules():
            if name in self.kfac_layers:
                self.hooks.append(module.register_forward_hook(make_forward_hook(name)))
                self.hooks.append(module.register_backward_hook(make_backward_hook(name)))
    
    def remove_hooks(self):
        """Remove all registered hooks."""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
    
    def step(self):
        """Update step counter."""
        self.step_count += 1
    
    def should_update_factors(self) -> bool:
        """Check if K-FAC factors should be updated this step."""
        return self.step_count % self.update_freq == 0
    
    def apply_kfac_inverse(self, gradients: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Apply K-FAC inverse to gradients.
        
        Args:
            gradients: Dictionary of parameter gradients
            
        Returns:
            Dictionary of K-FAC transformed gradients
        """
        transformed_grads = {}
        
        for name, grad in gradients.items():
            # Find corresponding layer
            layer_name = None
            for kfac_name in self.kfac_layers:
                if name.startswith(kfac_name):
                    layer_name = kfac_name
                    break
            
            if layer_name and layer_name in self.kfac_layers:
                # Apply K-FAC inverse
                transformed_grads[name] = self.kfac_layers[layer_name].apply_inverse(grad)
            else:
                # No K-FAC available, use original gradient
                transformed_grads[name] = grad
        
        return transformed_grads
    
    def __del__(self):
        """Cleanup hooks on deletion."""
        self.remove_hooks()
