#!/usr/bin/env python3
"""
Comprehensive test script to verify K-FAC influence function dimension fixes.
"""

import torch
import torch.nn as nn
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_different_architectures():
    """Test influence functions with different model architectures."""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Testing on device: {device}")
    
    # Test configurations
    test_configs = [
        {
            'name': 'Small Transformer',
            'vocab_size': 100,
            'hidden_size': 64,
            'seq_len': 16,
            'batch_size': 2
        },
        {
            'name': 'Medium Transformer',
            'vocab_size': 500,
            'hidden_size': 128,
            'seq_len': 32,
            'batch_size': 1
        }
    ]
    
    for config in test_configs:
        print(f"\n=== Testing {config['name']} ===")
        
        # Create model
        class TestTransformer(nn.Module):
            def __init__(self, vocab_size, hidden_size):
                super().__init__()
                self.embedding = nn.Embedding(vocab_size, hidden_size)
                self.layer1 = nn.Linear(hidden_size, hidden_size)
                self.layer2 = nn.Linear(hidden_size, hidden_size)
                self.output = nn.Linear(hidden_size, vocab_size)
                
            def forward(self, input_ids, attention_mask=None, **kwargs):
                x = self.embedding(input_ids)
                x = x + torch.relu(self.layer1(x))
                x = x + torch.relu(self.layer2(x))
                logits = self.output(x)
                return type('Output', (), {'logits': logits})()
        
        model = TestTransformer(config['vocab_size'], config['hidden_size']).to(device)
        
        # Create test data
        validation_batch = {
            'input_ids': torch.randint(0, config['vocab_size'], 
                                     (config['batch_size'], config['seq_len'])).to(device),
            'attention_mask': torch.ones(config['batch_size'], config['seq_len']).to(device)
        }
        
        training_sample = {
            'input_ids': torch.randint(0, config['vocab_size'], (1, config['seq_len'])).to(device),
            'attention_mask': torch.ones(1, config['seq_len']).to(device)
        }
        
        # Define loss function
        def loss_fn(model_output, batch_data):
            logits = model_output.logits
            labels = batch_data['input_ids']
            logits_flat = logits.view(-1, logits.size(-1))
            labels_flat = labels.view(-1)
            return torch.nn.functional.cross_entropy(logits_flat, labels_flat, ignore_index=-100)
        
        # Test influence function
        try:
            from verl.utils.influence_functions import create_influence_calculator
            
            influence_config = {
                'use_kfac': True,
                'regularization_lambda': 1e-3,
                'damping_factor': 1e-3,
                'kfac_damping': 1e-3,
                'max_samples_per_batch': 1
            }
            
            calculator = create_influence_calculator(model=model, config=influence_config)
            
            print(f"  Computing validation gradients...")
            calculator.compute_validation_gradients(validation_batch, loss_fn)
            
            print(f"  Computing influence score...")
            influence_score = calculator.compute_influence_score(
                training_sample, validation_batch, loss_fn
            )
            
            print(f"  ✅ {config['name']}: Influence score = {influence_score:.6f}")
            
            # Check memory usage
            if torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated() / 1024**2  # MB
                print(f"  Memory allocated: {memory_allocated:.1f} MB")
                
        except Exception as e:
            print(f"  ❌ {config['name']}: Failed with error: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

def test_dimension_validation():
    """Test that dimension validation works correctly."""
    print("\n=== Testing Dimension Validation ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create a model with known dimensions
    class SimpleLinearModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.linear1 = nn.Linear(10, 20)  # 10 -> 20
            self.linear2 = nn.Linear(20, 5)   # 20 -> 5
            
        def forward(self, input_ids, **kwargs):
            # Simulate embedding lookup
            x = torch.randn(input_ids.shape[0], input_ids.shape[1], 10, device=input_ids.device)
            x = torch.relu(self.linear1(x))
            logits = self.linear2(x)
            return type('Output', (), {'logits': logits})()
    
    model = SimpleLinearModel().to(device)
    
    # Create test data
    batch = {
        'input_ids': torch.randint(0, 100, (2, 8)).to(device),
        'attention_mask': torch.ones(2, 8).to(device)
    }
    
    def loss_fn(model_output, batch_data):
        logits = model_output.logits
        labels = torch.randint(0, 5, logits.shape[:2]).to(logits.device)
        return torch.nn.functional.cross_entropy(
            logits.view(-1, logits.size(-1)),
            labels.view(-1)
        )
    
    try:
        from verl.utils.influence_functions import KFACInfluenceFunctionCalculator
        
        calculator = KFACInfluenceFunctionCalculator(
            model=model,
            regularization_lambda=1e-3,
            kfac_damping=1e-3,
            use_kfac=True
        )
        
        # Extract K-FAC factors and validate
        calculator.extract_kfac_factors(batch, loss_fn)
        
        print("  K-FAC factors extracted:")
        for key, factor in calculator.kfac_factors.items():
            print(f"    {key}: {factor.shape}")
        
        # Test that factors are square matrices
        for key, factor in calculator.kfac_factors.items():
            if factor.shape[0] != factor.shape[1]:
                print(f"  ❌ Factor {key} is not square: {factor.shape}")
                return False
        
        print("  ✅ All K-FAC factors have correct dimensions")
        return True
        
    except Exception as e:
        print(f"  ❌ Dimension validation failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Running comprehensive influence function tests...")
    
    success1 = test_different_architectures()
    success2 = test_dimension_validation()
    
    if success1 and success2:
        print("\n🎉 All tests passed! K-FAC dimension fixes are working correctly.")
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
