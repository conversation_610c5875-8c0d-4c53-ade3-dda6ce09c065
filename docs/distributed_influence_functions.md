# Distributed Influence Functions Implementation

This document describes the distributed implementation of influence functions that works with Ray worker groups and FSDP (Fully Sharded Data Parallel) models.

## Overview

The distributed influence function implementation addresses the challenge of computing influence functions on large-scale models that are distributed across multiple GPUs using Ray and FSDP. Instead of trying to access the model directly on the driver process, the computation is distributed to the worker processes where the model shards reside.

## Architecture

### Key Components

1. **DistributedInfluenceFunctionCalculator**: Coordinates influence function computation across Ray workers
2. **Worker Methods**: Added to FSDP workers to handle influence function computation
3. **DataProto Integration**: Uses the existing data protocol for efficient communication
4. **Automatic Fallback**: Gracefully handles failures and falls back to original methods

### Distributed Flow

```
Driver Process                    Worker Processes
     |                                   |
     |-- create_influence_calculator --> |-- init_influence_functions
     |                                   |   (initialize calculator on each worker)
     |                                   |
     |-- compute_validation_gradients -> |-- compute validation gradients
     |                                   |   (cache gradients on each worker)
     |                                   |
     |-- compute_influence_scores -----> |-- compute influence scores
     |                                   |   (process training samples)
     |                                   |
     |<-- aggregated results ----------- |-- return influence scores
```

## Implementation Details

### Worker Methods

Three new methods were added to `ActorRolloutRefWorker` in `verl/workers/fsdp_workers.py`:

#### 1. `init_influence_functions(config)`
- **Purpose**: Initialize influence function calculator on each worker
- **Dispatch Mode**: `ONE_TO_ALL` (runs on all workers)
- **Functionality**: Creates a local influence calculator using the worker's model shard

#### 2. `compute_validation_gradients(data: DataProto)`
- **Purpose**: Compute and cache validation gradients
- **Dispatch Mode**: `DP_COMPUTE_PROTO` (distributed computation)
- **Functionality**: Computes gradients on validation data and caches them for later use

#### 3. `compute_influence_scores(data: DataProto)`
- **Purpose**: Compute influence scores for training samples
- **Dispatch Mode**: `DP_COMPUTE_PROTO` (distributed computation)
- **Functionality**: Processes training samples and returns influence scores

### FSDP Integration

The implementation properly handles FSDP-specific concerns:

- **Model Loading/Offloading**: Automatically loads model to GPU when needed and offloads back to CPU
- **Parameter Sharding**: Works with sharded model parameters across workers
- **Memory Management**: Respects FSDP memory management patterns

### Data Flow

1. **Initialization**: 
   ```python
   # Driver creates distributed calculator
   calculator = create_influence_calculator(worker_group=worker_group, config=config)
   ```

2. **Validation Gradient Computation**:
   ```python
   # Compute once and cache on all workers
   val_data_proto = DataProto.from_single_dict(validation_batch)
   calculator.compute_validation_gradients(val_data_proto)
   ```

3. **Influence Score Computation**:
   ```python
   # Process training batch across workers
   train_data_proto = DataProto.from_single_dict(training_batch)
   result = calculator.compute_influence_scores(train_data_proto)
   influence_scores = result.batch['influence_scores']
   ```

## Configuration

The distributed implementation is automatically enabled when using Ray workers. No special configuration is needed beyond enabling influence functions:

```yaml
data:
  use_influence_functions: true
  influence_use_kfac: true
  influence_regularization_lambda: 1e-3
  influence_damping_factor: 1e-3
  influence_max_samples_per_batch: 32
```

## Performance Considerations

### Advantages

1. **Scalability**: Computation scales with the number of workers
2. **Memory Efficiency**: Each worker only processes its model shard
3. **Parallel Processing**: Training samples processed in parallel across workers
4. **FSDP Compatibility**: Works seamlessly with existing FSDP setup

### Optimizations

1. **Validation Gradient Caching**: Validation gradients computed once and reused
2. **Batch Processing**: Training samples processed in configurable batch sizes
3. **K-FAC Acceleration**: K-FAC approximation reduces computational complexity
4. **Automatic Offloading**: Respects FSDP parameter offloading patterns

## Error Handling

The implementation includes robust error handling:

1. **Graceful Degradation**: Falls back to original reward-advantage formula if influence computation fails
2. **Per-Sample Fallback**: Individual sample failures don't affect the entire batch
3. **Configuration Validation**: Validates influence function parameters before use
4. **Worker Synchronization**: Handles worker failures and communication issues

## Monitoring

Enhanced wandb metrics track distributed influence function performance:

- `curriculum/influence_functions_enabled`: Whether influence functions are active
- `curriculum/influence_functions_active_ratio`: Ratio of sources using influence functions
- `curriculum/mean_influence_score_overall`: Overall influence score across all sources
- Per-source metrics for detailed monitoring

## Comparison with Single-Node Implementation

| Aspect | Single-Node | Distributed |
|--------|-------------|-------------|
| Model Access | Direct model reference | Worker group coordination |
| Computation | Single GPU/CPU | Multiple workers in parallel |
| Memory Usage | Full model in memory | Sharded across workers |
| Scalability | Limited by single device | Scales with worker count |
| Complexity | Simple direct calls | Coordinated worker communication |
| FSDP Support | Limited | Full integration |

## Future Improvements

1. **Gradient Compression**: Compress gradients for faster communication
2. **Adaptive Batching**: Dynamically adjust batch sizes based on worker load
3. **Hierarchical Aggregation**: Use tree-based aggregation for large worker counts
4. **Asynchronous Computation**: Overlap influence computation with training
5. **Checkpointing**: Save and restore influence function state across training runs

## Testing

The distributed implementation includes comprehensive tests:

- Unit tests for distributed calculator
- Integration tests with mock worker groups
- Worker method signature validation
- End-to-end flow testing

Run tests with:
```bash
python -m pytest tests/test_influence_functions.py -v
```

## Troubleshooting

### Common Issues

1. **Worker Initialization Failure**: Check that workers have access to influence function modules
2. **Memory Issues**: Reduce `influence_max_samples_per_batch` or enable parameter offloading
3. **Communication Timeouts**: Increase Ray timeout settings for large models
4. **FSDP Compatibility**: Ensure FSDP is properly configured with the influence function setup

### Debug Mode

Enable debug logging to monitor distributed computation:

```python
import logging
logging.getLogger('verl.utils.influence_functions').setLevel(logging.DEBUG)
```

This will provide detailed logs of worker communication and computation progress.
