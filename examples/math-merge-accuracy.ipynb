{"cells": [{"cell_type": "code", "execution_count": 1, "id": "12f6fe90", "metadata": {}, "outputs": [], "source": ["# FILE_FOLDER = \"~/data/gsm8k_d/train_harder.parquet\"\n", "FILE_FOLDER1 = \"~/ne-data/math/train.parquet\"\n", "FILE_FOLDER2 = \"/data/yzr/DUMP/examples/data/math-train-extracted-rate.parquet\"\n", "\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "41a12a0f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>level</th>\n", "      <th>type</th>\n", "      <th>data_source</th>\n", "      <th>prompt</th>\n", "      <th>ability</th>\n", "      <th>reward_model</th>\n", "      <th>extra_info</th>\n", "      <th>cluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Level 4</td>\n", "      <td>Intermediate Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Say that an integer $A$ is yummy...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-2013', 'style': 'rule'}</td>\n", "      <td>{'index': 4664, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Level 4</td>\n", "      <td>Intermediate Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'The equations\n", "\\[75x^4 + ax^3 + b...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-\\frac{1}{3}', 'style': 'rule'}</td>\n", "      <td>{'index': 4411, 'split': 'train'}</td>\n", "      <td>math_cluster_7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Level 3</td>\n", "      <td>Precalculus</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Find\n", "\\[\\sin \\left( \\sin^{-1} \\fr...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '\\frac{11 \\sqrt{5}}{25}', 'st...</td>\n", "      <td>{'index': 7448, 'split': 'train'}</td>\n", "      <td>math_cluster_9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Level 3</td>\n", "      <td>Counting &amp; Probability</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Interior numbers begin in the th...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '62', 'style': 'rule'}</td>\n", "      <td>{'index': 1919, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Level 2</td>\n", "      <td>Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Let $f(x)=2x^4+x^3+x^2-3x+r$. Fo...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-38', 'style': 'rule'}</td>\n", "      <td>{'index': 1298, 'split': 'train'}</td>\n", "      <td>math_cluster_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5995</th>\n", "      <td>Level 3</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Convert $314_{10}$ to base 6. Le...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '1242_6', 'style': 'rule'}</td>\n", "      <td>{'index': 5191, 'split': 'train'}</td>\n", "      <td>math_cluster_1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5996</th>\n", "      <td>Level 3</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'When the binary number $10010111...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '2', 'style': 'rule'}</td>\n", "      <td>{'index': 5226, 'split': 'train'}</td>\n", "      <td>math_cluster_4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5997</th>\n", "      <td>Level 5</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Let $f(n)$ be a function that, g...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': 'n = 255', 'style': 'rule'}</td>\n", "      <td>{'index': 5390, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5998</th>\n", "      <td>Level 1</td>\n", "      <td>Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'What is $\\sqrt[4]{16} \\cdot \\sqr...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '8', 'style': 'rule'}</td>\n", "      <td>{'index': 860, 'split': 'train'}</td>\n", "      <td>math_cluster_3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5999</th>\n", "      <td>Level 4</td>\n", "      <td>Precalculus</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Given that\n", "\\[2^{-\\frac{3}{2} + 2...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '\\frac{1}{8}', 'style': 'rule'}</td>\n", "      <td>{'index': 7270, 'split': 'train'}</td>\n", "      <td>math_cluster_3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6000 rows × 8 columns</p>\n", "</div>"], "text/plain": ["        level                    type                         data_source  \\\n", "0     Level 4    Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "1     Level 4    Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "2     Level 3             Precalculus  DigitalLearningGmbH/MATH-lighteval   \n", "3     Level 3  Counting & Probability  DigitalLearningGmbH/MATH-lighteval   \n", "4     Level 2                 Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "...       ...                     ...                                 ...   \n", "5995  Level 3           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5996  Level 3           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5997  Level 5           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5998  Level 1                 Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "5999  Level 4             Precalculus  DigitalLearningGmbH/MATH-lighteval   \n", "\n", "                                                 prompt ability  \\\n", "0     [{'content': 'Say that an integer $A$ is yummy...    math   \n", "1     [{'content': 'The equations\n", "\\[75x^4 + ax^3 + b...    math   \n", "2     [{'content': 'Find\n", "\\[\\sin \\left( \\sin^{-1} \\fr...    math   \n", "3     [{'content': 'Interior numbers begin in the th...    math   \n", "4     [{'content': 'Let $f(x)=2x^4+x^3+x^2-3x+r$. Fo...    math   \n", "...                                                 ...     ...   \n", "5995  [{'content': 'Convert $314_{10}$ to base 6. Le...    math   \n", "5996  [{'content': 'When the binary number $10010111...    math   \n", "5997  [{'content': 'Let $f(n)$ be a function that, g...    math   \n", "5998  [{'content': 'What is $\\sqrt[4]{16} \\cdot \\sqr...    math   \n", "5999  [{'content': 'Given that\n", "\\[2^{-\\frac{3}{2} + 2...    math   \n", "\n", "                                           reward_model  \\\n", "0            {'ground_truth': '-2013', 'style': 'rule'}   \n", "1     {'ground_truth': '-\\frac{1}{3}', 'style': 'rule'}   \n", "2     {'ground_truth': '\\frac{11 \\sqrt{5}}{25}', 'st...   \n", "3               {'ground_truth': '62', 'style': 'rule'}   \n", "4              {'ground_truth': '-38', 'style': 'rule'}   \n", "...                                                 ...   \n", "5995        {'ground_truth': '1242_6', 'style': 'rule'}   \n", "5996             {'ground_truth': '2', 'style': 'rule'}   \n", "5997       {'ground_truth': 'n = 255', 'style': 'rule'}   \n", "5998             {'ground_truth': '8', 'style': 'rule'}   \n", "5999   {'ground_truth': '\\frac{1}{8}', 'style': 'rule'}   \n", "\n", "                             extra_info         cluster  \n", "0     {'index': 4664, 'split': 'train'}  math_cluster_0  \n", "1     {'index': 4411, 'split': 'train'}  math_cluster_7  \n", "2     {'index': 7448, 'split': 'train'}  math_cluster_9  \n", "3     {'index': 1919, 'split': 'train'}  math_cluster_0  \n", "4     {'index': 1298, 'split': 'train'}  math_cluster_1  \n", "...                                 ...             ...  \n", "5995  {'index': 5191, 'split': 'train'}  math_cluster_1  \n", "5996  {'index': 5226, 'split': 'train'}  math_cluster_4  \n", "5997  {'index': 5390, 'split': 'train'}  math_cluster_0  \n", "5998   {'index': 860, 'split': 'train'}  math_cluster_3  \n", "5999  {'index': 7270, 'split': 'train'}  math_cluster_3  \n", "\n", "[6000 rows x 8 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df1 = pd.read_parquet(FILE_FOLDER1)\n", "df1"]}, {"cell_type": "code", "execution_count": 3, "id": "ddf8ae65", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>problem</th>\n", "      <th>level</th>\n", "      <th>solution</th>\n", "      <th>type</th>\n", "      <th>solution-extracted</th>\n", "      <th>accuracy</th>\n", "      <th>k_repetitions</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Let \\[f(x) = \\left\\{\\n\\begin{array}{cl} ax+3, ...</td>\n", "      <td>Level 5</td>\n", "      <td>For the piecewise function to be continuous, t...</td>\n", "      <td>Algebra</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A rectangular band formation is a formation wi...</td>\n", "      <td>Level 5</td>\n", "      <td>Let $x$ be the number of band members in each ...</td>\n", "      <td>Algebra</td>\n", "      <td>98</td>\n", "      <td>0.4</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>What is the degree of the polynomial $(4 +5x^3...</td>\n", "      <td>Level 3</td>\n", "      <td>This polynomial is not written in standard for...</td>\n", "      <td>Algebra</td>\n", "      <td>4</td>\n", "      <td>0.9</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Evaluate $\\left\\lceil3\\left(6-\\frac12\\right)\\r...</td>\n", "      <td>Level 3</td>\n", "      <td>Firstly, $3\\left(6-\\frac12\\right)=18-1-\\frac12...</td>\n", "      <td>Algebra</td>\n", "      <td>17</td>\n", "      <td>0.2</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Sam is hired for a 20-day period. On days that...</td>\n", "      <td>Level 3</td>\n", "      <td>Call $x$ the number of days Sam works and $y$ ...</td>\n", "      <td>Algebra</td>\n", "      <td>6</td>\n", "      <td>0.1</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7495</th>\n", "      <td>Find the number of real solutions of the equat...</td>\n", "      <td>Level 3</td>\n", "      <td>Since $-1 \\le \\sin x \\le 1,$ all solutions mus...</td>\n", "      <td>Precalculus</td>\n", "      <td>63</td>\n", "      <td>0.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7496</th>\n", "      <td>Let $A,$ $B,$ $C$ be the angles of a triangle....</td>\n", "      <td>Level 2</td>\n", "      <td>We can expand the determinant as follows:\\n\\be...</td>\n", "      <td>Precalculus</td>\n", "      <td>0</td>\n", "      <td>0.8</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7497</th>\n", "      <td>Let $G$ be the centroid of triangle $ABC,$ and...</td>\n", "      <td>Level 2</td>\n", "      <td>Let $\\mathbf{a}$ denote $\\overrightarrow{A},$ ...</td>\n", "      <td>Precalculus</td>\n", "      <td>3</td>\n", "      <td>0.7</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7498</th>\n", "      <td>If angle $A$ lies in the second quadrant and $...</td>\n", "      <td>Level 2</td>\n", "      <td>Since angle $A$ lies in the second quadrant, $...</td>\n", "      <td>Precalculus</td>\n", "      <td>-\\frac{\\sqrt{7}}{4}</td>\n", "      <td>1.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7499</th>\n", "      <td>The real numbers $a$ and $b$ satisfy\\n\\[\\begin...</td>\n", "      <td>Level 2</td>\n", "      <td>In general, $\\mathbf{v} \\times \\mathbf{w} = \\m...</td>\n", "      <td>Precalculus</td>\n", "      <td>\\left( \\frac{8}{5}, -\\frac{35}{2} \\right)</td>\n", "      <td>0.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7500 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                                                problem    level  \\\n", "0     Let \\[f(x) = \\left\\{\\n\\begin{array}{cl} ax+3, ...  Level 5   \n", "1     A rectangular band formation is a formation wi...  Level 5   \n", "2     What is the degree of the polynomial $(4 +5x^3...  Level 3   \n", "3     Evaluate $\\left\\lceil3\\left(6-\\frac12\\right)\\r...  Level 3   \n", "4     Sam is hired for a 20-day period. On days that...  Level 3   \n", "...                                                 ...      ...   \n", "7495  Find the number of real solutions of the equat...  Level 3   \n", "7496  Let $A,$ $B,$ $C$ be the angles of a triangle....  Level 2   \n", "7497  Let $G$ be the centroid of triangle $ABC,$ and...  Level 2   \n", "7498  If angle $A$ lies in the second quadrant and $...  Level 2   \n", "7499  The real numbers $a$ and $b$ satisfy\\n\\[\\begin...  Level 2   \n", "\n", "                                               solution         type  \\\n", "0     For the piecewise function to be continuous, t...      Algebra   \n", "1     Let $x$ be the number of band members in each ...      Algebra   \n", "2     This polynomial is not written in standard for...      Algebra   \n", "3     Firstly, $3\\left(6-\\frac12\\right)=18-1-\\frac12...      Algebra   \n", "4     Call $x$ the number of days Sam works and $y$ ...      Algebra   \n", "...                                                 ...          ...   \n", "7495  Since $-1 \\le \\sin x \\le 1,$ all solutions mus...  Precalculus   \n", "7496  We can expand the determinant as follows:\\n\\be...  Precalculus   \n", "7497  Let $\\mathbf{a}$ denote $\\overrightarrow{A},$ ...  Precalculus   \n", "7498  Since angle $A$ lies in the second quadrant, $...  Precalculus   \n", "7499  In general, $\\mathbf{v} \\times \\mathbf{w} = \\m...  Precalculus   \n", "\n", "                             solution-extracted  accuracy  k_repetitions  \n", "0                                             0       0.0             10  \n", "1                                            98       0.4             10  \n", "2                                             4       0.9             10  \n", "3                                            17       0.2             10  \n", "4                                             6       0.1             10  \n", "...                                         ...       ...            ...  \n", "7495                                         63       0.0             10  \n", "7496                                          0       0.8             10  \n", "7497                                          3       0.7             10  \n", "7498                        -\\frac{\\sqrt{7}}{4}       1.0             10  \n", "7499  \\left( \\frac{8}{5}, -\\frac{35}{2} \\right)       0.0             10  \n", "\n", "[7500 rows x 7 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df2 =  pd.read_parquet(FILE_FOLDER2)\n", "df2"]}, {"cell_type": "code", "execution_count": 4, "id": "a8372792", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>level</th>\n", "      <th>type</th>\n", "      <th>data_source</th>\n", "      <th>prompt</th>\n", "      <th>ability</th>\n", "      <th>reward_model</th>\n", "      <th>extra_info</th>\n", "      <th>cluster</th>\n", "      <th>question</th>\n", "      <th>matched_problem</th>\n", "      <th>match_score</th>\n", "      <th>problem</th>\n", "      <th>accuracy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Level 4</td>\n", "      <td>Intermediate Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Say that an integer $A$ is yummy...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-2013', 'style': 'rule'}</td>\n", "      <td>{'index': 4664, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "      <td>Say that an integer $A$ is yummy if there exis...</td>\n", "      <td>Say that an integer $A$ is yummy if there exis...</td>\n", "      <td>81.081081</td>\n", "      <td>Say that an integer $A$ is yummy if there exis...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Level 4</td>\n", "      <td>Intermediate Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'The equations\n", "\\[75x^4 + ax^3 + b...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-\\frac{1}{3}', 'style': 'rule'}</td>\n", "      <td>{'index': 4411, 'split': 'train'}</td>\n", "      <td>math_cluster_7</td>\n", "      <td>The equations\\n\\[75x^4 + ax^3 + bx^2 + cx + 12...</td>\n", "      <td>The equations\\n\\[75x^4 + ax^3 + bx^2 + cx + 12...</td>\n", "      <td>84.581498</td>\n", "      <td>The equations\\n\\[75x^4 + ax^3 + bx^2 + cx + 12...</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Level 3</td>\n", "      <td>Precalculus</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Find\n", "\\[\\sin \\left( \\sin^{-1} \\fr...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '\\frac{11 \\sqrt{5}}{25}', 'st...</td>\n", "      <td>{'index': 7448, 'split': 'train'}</td>\n", "      <td>math_cluster_9</td>\n", "      <td>Find\\n\\[\\sin \\left( \\sin^{-1} \\frac{3}{5} + \\t...</td>\n", "      <td>Find\\n\\[\\sin \\left( \\sin^{-1} \\frac{3}{5} + \\t...</td>\n", "      <td>65.000000</td>\n", "      <td>Find\\n\\[\\sin \\left( \\sin^{-1} \\frac{3}{5} + \\t...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Level 3</td>\n", "      <td>Counting &amp; Probability</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Interior numbers begin in the th...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '62', 'style': 'rule'}</td>\n", "      <td>{'index': 1919, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "      <td>Interior numbers begin in the third row of Pas...</td>\n", "      <td>Interior numbers begin in the third row of Pas...</td>\n", "      <td>86.940299</td>\n", "      <td>Interior numbers begin in the third row of Pas...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Level 2</td>\n", "      <td>Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Let $f(x)=2x^4+x^3+x^2-3x+r$. Fo...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-38', 'style': 'rule'}</td>\n", "      <td>{'index': 1298, 'split': 'train'}</td>\n", "      <td>math_cluster_1</td>\n", "      <td>Let $f(x)=2x^4+x^3+x^2-3x+r$. For what value o...</td>\n", "      <td>Let $f(x)=2x^4+x^3+x^2-3x+r$. For what value o...</td>\n", "      <td>64.646465</td>\n", "      <td>Let $f(x)=2x^4+x^3+x^2-3x+r$. For what value o...</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5995</th>\n", "      <td>Level 3</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Convert $314_{10}$ to base 6. Le...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '1242_6', 'style': 'rule'}</td>\n", "      <td>{'index': 5191, 'split': 'train'}</td>\n", "      <td>math_cluster_1</td>\n", "      <td>Convert $314_{10}$ to base 6. Let's think step...</td>\n", "      <td>Convert $199_{10}$ to base 2. Let $x$ be the n...</td>\n", "      <td>54.385965</td>\n", "      <td>Convert $199_{10}$ to base 2. Let $x$ be the n...</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5996</th>\n", "      <td>Level 3</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'When the binary number $10010111...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '2', 'style': 'rule'}</td>\n", "      <td>{'index': 5226, 'split': 'train'}</td>\n", "      <td>math_cluster_4</td>\n", "      <td>When the binary number $100101110010_2$ is div...</td>\n", "      <td>When the binary number $100101110010_2$ is div...</td>\n", "      <td>75.694444</td>\n", "      <td>When the binary number $100101110010_2$ is div...</td>\n", "      <td>0.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5997</th>\n", "      <td>Level 5</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Let $f(n)$ be a function that, g...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': 'n = 255', 'style': 'rule'}</td>\n", "      <td>{'index': 5390, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "      <td>Let $f(n)$ be a function that, given an intege...</td>\n", "      <td>Let $f(n)$ be a function that, given an intege...</td>\n", "      <td>87.632509</td>\n", "      <td>Let $f(n)$ be a function that, given an intege...</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5998</th>\n", "      <td>Level 1</td>\n", "      <td>Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'What is $\\sqrt[4]{16} \\cdot \\sqr...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '8', 'style': 'rule'}</td>\n", "      <td>{'index': 860, 'split': 'train'}</td>\n", "      <td>math_cluster_3</td>\n", "      <td>What is $\\sqrt[4]{16} \\cdot \\sqrt[3]{8} \\cdot ...</td>\n", "      <td>What is $\\sqrt[4]{16} \\cdot \\sqrt[3]{8} \\cdot ...</td>\n", "      <td>71.544715</td>\n", "      <td>What is $\\sqrt[4]{16} \\cdot \\sqrt[3]{8} \\cdot ...</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5999</th>\n", "      <td>Level 4</td>\n", "      <td>Precalculus</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Given that\n", "\\[2^{-\\frac{3}{2} + 2...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '\\frac{1}{8}', 'style': 'rule'}</td>\n", "      <td>{'index': 7270, 'split': 'train'}</td>\n", "      <td>math_cluster_3</td>\n", "      <td>Given that\\n\\[2^{-\\frac{3}{2} + 2 \\cos \\theta}...</td>\n", "      <td>Given that\\n\\[2^{-\\frac{3}{2} + 2 \\cos \\theta}...</td>\n", "      <td>75.524476</td>\n", "      <td>Given that\\n\\[2^{-\\frac{3}{2} + 2 \\cos \\theta}...</td>\n", "      <td>0.3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6000 rows × 13 columns</p>\n", "</div>"], "text/plain": ["        level                    type                         data_source  \\\n", "0     Level 4    Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "1     Level 4    Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "2     Level 3             Precalculus  DigitalLearningGmbH/MATH-lighteval   \n", "3     Level 3  Counting & Probability  DigitalLearningGmbH/MATH-lighteval   \n", "4     Level 2                 Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "...       ...                     ...                                 ...   \n", "5995  Level 3           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5996  Level 3           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5997  Level 5           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5998  Level 1                 Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "5999  Level 4             Precalculus  DigitalLearningGmbH/MATH-lighteval   \n", "\n", "                                                 prompt ability  \\\n", "0     [{'content': 'Say that an integer $A$ is yummy...    math   \n", "1     [{'content': 'The equations\n", "\\[75x^4 + ax^3 + b...    math   \n", "2     [{'content': 'Find\n", "\\[\\sin \\left( \\sin^{-1} \\fr...    math   \n", "3     [{'content': 'Interior numbers begin in the th...    math   \n", "4     [{'content': 'Let $f(x)=2x^4+x^3+x^2-3x+r$. Fo...    math   \n", "...                                                 ...     ...   \n", "5995  [{'content': 'Convert $314_{10}$ to base 6. Le...    math   \n", "5996  [{'content': 'When the binary number $10010111...    math   \n", "5997  [{'content': 'Let $f(n)$ be a function that, g...    math   \n", "5998  [{'content': 'What is $\\sqrt[4]{16} \\cdot \\sqr...    math   \n", "5999  [{'content': 'Given that\n", "\\[2^{-\\frac{3}{2} + 2...    math   \n", "\n", "                                           reward_model  \\\n", "0            {'ground_truth': '-2013', 'style': 'rule'}   \n", "1     {'ground_truth': '-\\frac{1}{3}', 'style': 'rule'}   \n", "2     {'ground_truth': '\\frac{11 \\sqrt{5}}{25}', 'st...   \n", "3               {'ground_truth': '62', 'style': 'rule'}   \n", "4              {'ground_truth': '-38', 'style': 'rule'}   \n", "...                                                 ...   \n", "5995        {'ground_truth': '1242_6', 'style': 'rule'}   \n", "5996             {'ground_truth': '2', 'style': 'rule'}   \n", "5997       {'ground_truth': 'n = 255', 'style': 'rule'}   \n", "5998             {'ground_truth': '8', 'style': 'rule'}   \n", "5999   {'ground_truth': '\\frac{1}{8}', 'style': 'rule'}   \n", "\n", "                             extra_info         cluster  \\\n", "0     {'index': 4664, 'split': 'train'}  math_cluster_0   \n", "1     {'index': 4411, 'split': 'train'}  math_cluster_7   \n", "2     {'index': 7448, 'split': 'train'}  math_cluster_9   \n", "3     {'index': 1919, 'split': 'train'}  math_cluster_0   \n", "4     {'index': 1298, 'split': 'train'}  math_cluster_1   \n", "...                                 ...             ...   \n", "5995  {'index': 5191, 'split': 'train'}  math_cluster_1   \n", "5996  {'index': 5226, 'split': 'train'}  math_cluster_4   \n", "5997  {'index': 5390, 'split': 'train'}  math_cluster_0   \n", "5998   {'index': 860, 'split': 'train'}  math_cluster_3   \n", "5999  {'index': 7270, 'split': 'train'}  math_cluster_3   \n", "\n", "                                               question  \\\n", "0     Say that an integer $A$ is yummy if there exis...   \n", "1     The equations\\n\\[75x^4 + ax^3 + bx^2 + cx + 12...   \n", "2     Find\\n\\[\\sin \\left( \\sin^{-1} \\frac{3}{5} + \\t...   \n", "3     Interior numbers begin in the third row of Pas...   \n", "4     Let $f(x)=2x^4+x^3+x^2-3x+r$. For what value o...   \n", "...                                                 ...   \n", "5995  Convert $314_{10}$ to base 6. Let's think step...   \n", "5996  When the binary number $100101110010_2$ is div...   \n", "5997  Let $f(n)$ be a function that, given an intege...   \n", "5998  What is $\\sqrt[4]{16} \\cdot \\sqrt[3]{8} \\cdot ...   \n", "5999  Given that\\n\\[2^{-\\frac{3}{2} + 2 \\cos \\theta}...   \n", "\n", "                                        matched_problem  match_score  \\\n", "0     Say that an integer $A$ is yummy if there exis...    81.081081   \n", "1     The equations\\n\\[75x^4 + ax^3 + bx^2 + cx + 12...    84.581498   \n", "2     Find\\n\\[\\sin \\left( \\sin^{-1} \\frac{3}{5} + \\t...    65.000000   \n", "3     Interior numbers begin in the third row of Pas...    86.940299   \n", "4     Let $f(x)=2x^4+x^3+x^2-3x+r$. For what value o...    64.646465   \n", "...                                                 ...          ...   \n", "5995  Convert $199_{10}$ to base 2. Let $x$ be the n...    54.385965   \n", "5996  When the binary number $100101110010_2$ is div...    75.694444   \n", "5997  Let $f(n)$ be a function that, given an intege...    87.632509   \n", "5998  What is $\\sqrt[4]{16} \\cdot \\sqrt[3]{8} \\cdot ...    71.544715   \n", "5999  Given that\\n\\[2^{-\\frac{3}{2} + 2 \\cos \\theta}...    75.524476   \n", "\n", "                                                problem  accuracy  \n", "0     Say that an integer $A$ is yummy if there exis...       0.0  \n", "1     The equations\\n\\[75x^4 + ax^3 + bx^2 + cx + 12...       0.2  \n", "2     Find\\n\\[\\sin \\left( \\sin^{-1} \\frac{3}{5} + \\t...       0.0  \n", "3     Interior numbers begin in the third row of Pas...       0.0  \n", "4     Let $f(x)=2x^4+x^3+x^2-3x+r$. For what value o...       0.1  \n", "...                                                 ...       ...  \n", "5995  Convert $199_{10}$ to base 2. Let $x$ be the n...       0.2  \n", "5996  When the binary number $100101110010_2$ is div...       0.8  \n", "5997  Let $f(n)$ be a function that, given an intege...       0.1  \n", "5998  What is $\\sqrt[4]{16} \\cdot \\sqrt[3]{8} \\cdot ...       0.2  \n", "5999  Given that\\n\\[2^{-\\frac{3}{2} + 2 \\cos \\theta}...       0.3  \n", "\n", "[6000 rows x 13 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# prompt 是list，且第一个元素的content就是question\n", "df1['question'] = df1['prompt'].apply(lambda x: x[0]['content'])\n", "\n", "from rapidfuzz import process, fuzz\n", "\n", "# 假设df1['question']和df2['problem']都已准备好\n", "\n", "def match_problem(question, problems, scorer=fuzz.ratio):\n", "    # 取分数最高的匹配\n", "    match, score, idx = process.extractOne(question, problems, scorer=scorer)\n", "    return match, score\n", "\n", "# 为df1找出最相似的problem\n", "matches = df1['question'].apply(\n", "    lambda q: match_problem(q, df2['problem'].tolist())\n", ")\n", "\n", "df1['matched_problem'] = matches.apply(lambda x: x[0])\n", "df1['match_score'] = matches.apply(lambda x: x[1])\n", "\n", "# 通过matched_problem合并accuracy\n", "df_merged = df1.merge(df2[['problem', 'accuracy']], left_on='matched_problem', right_on='problem', how='left')\n", "df_merged"]}, {"cell_type": "code", "execution_count": 5, "id": "d74b8d0b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>level</th>\n", "      <th>type</th>\n", "      <th>data_source</th>\n", "      <th>prompt</th>\n", "      <th>ability</th>\n", "      <th>reward_model</th>\n", "      <th>extra_info</th>\n", "      <th>cluster</th>\n", "      <th>accuracy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Level 4</td>\n", "      <td>Intermediate Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Say that an integer $A$ is yummy...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-2013', 'style': 'rule'}</td>\n", "      <td>{'index': 4664, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Level 4</td>\n", "      <td>Intermediate Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'The equations\n", "\\[75x^4 + ax^3 + b...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-\\frac{1}{3}', 'style': 'rule'}</td>\n", "      <td>{'index': 4411, 'split': 'train'}</td>\n", "      <td>math_cluster_7</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Level 3</td>\n", "      <td>Precalculus</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Find\n", "\\[\\sin \\left( \\sin^{-1} \\fr...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '\\frac{11 \\sqrt{5}}{25}', 'st...</td>\n", "      <td>{'index': 7448, 'split': 'train'}</td>\n", "      <td>math_cluster_9</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Level 3</td>\n", "      <td>Counting &amp; Probability</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Interior numbers begin in the th...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '62', 'style': 'rule'}</td>\n", "      <td>{'index': 1919, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Level 2</td>\n", "      <td>Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Let $f(x)=2x^4+x^3+x^2-3x+r$. Fo...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-38', 'style': 'rule'}</td>\n", "      <td>{'index': 1298, 'split': 'train'}</td>\n", "      <td>math_cluster_1</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5995</th>\n", "      <td>Level 3</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Convert $314_{10}$ to base 6. Le...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '1242_6', 'style': 'rule'}</td>\n", "      <td>{'index': 5191, 'split': 'train'}</td>\n", "      <td>math_cluster_1</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5996</th>\n", "      <td>Level 3</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'When the binary number $10010111...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '2', 'style': 'rule'}</td>\n", "      <td>{'index': 5226, 'split': 'train'}</td>\n", "      <td>math_cluster_4</td>\n", "      <td>0.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5997</th>\n", "      <td>Level 5</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Let $f(n)$ be a function that, g...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': 'n = 255', 'style': 'rule'}</td>\n", "      <td>{'index': 5390, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "      <td>0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5998</th>\n", "      <td>Level 1</td>\n", "      <td>Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'What is $\\sqrt[4]{16} \\cdot \\sqr...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '8', 'style': 'rule'}</td>\n", "      <td>{'index': 860, 'split': 'train'}</td>\n", "      <td>math_cluster_3</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5999</th>\n", "      <td>Level 4</td>\n", "      <td>Precalculus</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Given that\n", "\\[2^{-\\frac{3}{2} + 2...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '\\frac{1}{8}', 'style': 'rule'}</td>\n", "      <td>{'index': 7270, 'split': 'train'}</td>\n", "      <td>math_cluster_3</td>\n", "      <td>0.3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6000 rows × 9 columns</p>\n", "</div>"], "text/plain": ["        level                    type                         data_source  \\\n", "0     Level 4    Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "1     Level 4    Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "2     Level 3             Precalculus  DigitalLearningGmbH/MATH-lighteval   \n", "3     Level 3  Counting & Probability  DigitalLearningGmbH/MATH-lighteval   \n", "4     Level 2                 Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "...       ...                     ...                                 ...   \n", "5995  Level 3           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5996  Level 3           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5997  Level 5           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5998  Level 1                 Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "5999  Level 4             Precalculus  DigitalLearningGmbH/MATH-lighteval   \n", "\n", "                                                 prompt ability  \\\n", "0     [{'content': 'Say that an integer $A$ is yummy...    math   \n", "1     [{'content': 'The equations\n", "\\[75x^4 + ax^3 + b...    math   \n", "2     [{'content': 'Find\n", "\\[\\sin \\left( \\sin^{-1} \\fr...    math   \n", "3     [{'content': 'Interior numbers begin in the th...    math   \n", "4     [{'content': 'Let $f(x)=2x^4+x^3+x^2-3x+r$. Fo...    math   \n", "...                                                 ...     ...   \n", "5995  [{'content': 'Convert $314_{10}$ to base 6. Le...    math   \n", "5996  [{'content': 'When the binary number $10010111...    math   \n", "5997  [{'content': 'Let $f(n)$ be a function that, g...    math   \n", "5998  [{'content': 'What is $\\sqrt[4]{16} \\cdot \\sqr...    math   \n", "5999  [{'content': 'Given that\n", "\\[2^{-\\frac{3}{2} + 2...    math   \n", "\n", "                                           reward_model  \\\n", "0            {'ground_truth': '-2013', 'style': 'rule'}   \n", "1     {'ground_truth': '-\\frac{1}{3}', 'style': 'rule'}   \n", "2     {'ground_truth': '\\frac{11 \\sqrt{5}}{25}', 'st...   \n", "3               {'ground_truth': '62', 'style': 'rule'}   \n", "4              {'ground_truth': '-38', 'style': 'rule'}   \n", "...                                                 ...   \n", "5995        {'ground_truth': '1242_6', 'style': 'rule'}   \n", "5996             {'ground_truth': '2', 'style': 'rule'}   \n", "5997       {'ground_truth': 'n = 255', 'style': 'rule'}   \n", "5998             {'ground_truth': '8', 'style': 'rule'}   \n", "5999   {'ground_truth': '\\frac{1}{8}', 'style': 'rule'}   \n", "\n", "                             extra_info         cluster  accuracy  \n", "0     {'index': 4664, 'split': 'train'}  math_cluster_0       0.0  \n", "1     {'index': 4411, 'split': 'train'}  math_cluster_7       0.2  \n", "2     {'index': 7448, 'split': 'train'}  math_cluster_9       0.0  \n", "3     {'index': 1919, 'split': 'train'}  math_cluster_0       0.0  \n", "4     {'index': 1298, 'split': 'train'}  math_cluster_1       0.1  \n", "...                                 ...             ...       ...  \n", "5995  {'index': 5191, 'split': 'train'}  math_cluster_1       0.2  \n", "5996  {'index': 5226, 'split': 'train'}  math_cluster_4       0.8  \n", "5997  {'index': 5390, 'split': 'train'}  math_cluster_0       0.1  \n", "5998   {'index': 860, 'split': 'train'}  math_cluster_3       0.2  \n", "5999  {'index': 7270, 'split': 'train'}  math_cluster_3       0.3  \n", "\n", "[6000 rows x 9 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_merged = df_merged.drop(columns=['question','matched_problem','match_score','problem'])\n", "df_merged"]}, {"cell_type": "code", "execution_count": 8, "id": "a48dd7b7", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>level</th>\n", "      <th>type</th>\n", "      <th>data_source</th>\n", "      <th>prompt</th>\n", "      <th>ability</th>\n", "      <th>reward_model</th>\n", "      <th>extra_info</th>\n", "      <th>cluster</th>\n", "      <th>accuracy</th>\n", "      <th>accuracy_bin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Level 4</td>\n", "      <td>Intermediate Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Say that an integer $A$ is yummy...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-2013', 'style': 'rule'}</td>\n", "      <td>{'index': 4664, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "      <td>0.0</td>\n", "      <td>gsm8k_abin_0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Level 4</td>\n", "      <td>Intermediate Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'The equations\n", "\\[75x^4 + ax^3 + b...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-\\frac{1}{3}', 'style': 'rule'}</td>\n", "      <td>{'index': 4411, 'split': 'train'}</td>\n", "      <td>math_cluster_7</td>\n", "      <td>0.2</td>\n", "      <td>gsm8k_abin_0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Level 3</td>\n", "      <td>Precalculus</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Find\n", "\\[\\sin \\left( \\sin^{-1} \\fr...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '\\frac{11 \\sqrt{5}}{25}', 'st...</td>\n", "      <td>{'index': 7448, 'split': 'train'}</td>\n", "      <td>math_cluster_9</td>\n", "      <td>0.0</td>\n", "      <td>gsm8k_abin_0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Level 3</td>\n", "      <td>Counting &amp; Probability</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Interior numbers begin in the th...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '62', 'style': 'rule'}</td>\n", "      <td>{'index': 1919, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "      <td>0.0</td>\n", "      <td>gsm8k_abin_0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Level 2</td>\n", "      <td>Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Let $f(x)=2x^4+x^3+x^2-3x+r$. Fo...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '-38', 'style': 'rule'}</td>\n", "      <td>{'index': 1298, 'split': 'train'}</td>\n", "      <td>math_cluster_1</td>\n", "      <td>0.1</td>\n", "      <td>gsm8k_abin_0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5995</th>\n", "      <td>Level 3</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Convert $314_{10}$ to base 6. Le...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '1242_6', 'style': 'rule'}</td>\n", "      <td>{'index': 5191, 'split': 'train'}</td>\n", "      <td>math_cluster_1</td>\n", "      <td>0.2</td>\n", "      <td>gsm8k_abin_0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5996</th>\n", "      <td>Level 3</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'When the binary number $10010111...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '2', 'style': 'rule'}</td>\n", "      <td>{'index': 5226, 'split': 'train'}</td>\n", "      <td>math_cluster_4</td>\n", "      <td>0.8</td>\n", "      <td>gsm8k_abin_0.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5997</th>\n", "      <td>Level 5</td>\n", "      <td>Number Theory</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Let $f(n)$ be a function that, g...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': 'n = 255', 'style': 'rule'}</td>\n", "      <td>{'index': 5390, 'split': 'train'}</td>\n", "      <td>math_cluster_0</td>\n", "      <td>0.1</td>\n", "      <td>gsm8k_abin_0.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5998</th>\n", "      <td>Level 1</td>\n", "      <td>Algebra</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'What is $\\sqrt[4]{16} \\cdot \\sqr...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '8', 'style': 'rule'}</td>\n", "      <td>{'index': 860, 'split': 'train'}</td>\n", "      <td>math_cluster_3</td>\n", "      <td>0.2</td>\n", "      <td>gsm8k_abin_0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5999</th>\n", "      <td>Level 4</td>\n", "      <td>Precalculus</td>\n", "      <td>DigitalLearningGmbH/MATH-lighteval</td>\n", "      <td>[{'content': 'Given that\n", "\\[2^{-\\frac{3}{2} + 2...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '\\frac{1}{8}', 'style': 'rule'}</td>\n", "      <td>{'index': 7270, 'split': 'train'}</td>\n", "      <td>math_cluster_3</td>\n", "      <td>0.3</td>\n", "      <td>gsm8k_abin_0.3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>6000 rows × 10 columns</p>\n", "</div>"], "text/plain": ["        level                    type                         data_source  \\\n", "0     Level 4    Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "1     Level 4    Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "2     Level 3             Precalculus  DigitalLearningGmbH/MATH-lighteval   \n", "3     Level 3  Counting & Probability  DigitalLearningGmbH/MATH-lighteval   \n", "4     Level 2                 Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "...       ...                     ...                                 ...   \n", "5995  Level 3           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5996  Level 3           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5997  Level 5           Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5998  Level 1                 Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "5999  Level 4             Precalculus  DigitalLearningGmbH/MATH-lighteval   \n", "\n", "                                                 prompt ability  \\\n", "0     [{'content': 'Say that an integer $A$ is yummy...    math   \n", "1     [{'content': 'The equations\n", "\\[75x^4 + ax^3 + b...    math   \n", "2     [{'content': 'Find\n", "\\[\\sin \\left( \\sin^{-1} \\fr...    math   \n", "3     [{'content': 'Interior numbers begin in the th...    math   \n", "4     [{'content': 'Let $f(x)=2x^4+x^3+x^2-3x+r$. Fo...    math   \n", "...                                                 ...     ...   \n", "5995  [{'content': 'Convert $314_{10}$ to base 6. Le...    math   \n", "5996  [{'content': 'When the binary number $10010111...    math   \n", "5997  [{'content': 'Let $f(n)$ be a function that, g...    math   \n", "5998  [{'content': 'What is $\\sqrt[4]{16} \\cdot \\sqr...    math   \n", "5999  [{'content': 'Given that\n", "\\[2^{-\\frac{3}{2} + 2...    math   \n", "\n", "                                           reward_model  \\\n", "0            {'ground_truth': '-2013', 'style': 'rule'}   \n", "1     {'ground_truth': '-\\frac{1}{3}', 'style': 'rule'}   \n", "2     {'ground_truth': '\\frac{11 \\sqrt{5}}{25}', 'st...   \n", "3               {'ground_truth': '62', 'style': 'rule'}   \n", "4              {'ground_truth': '-38', 'style': 'rule'}   \n", "...                                                 ...   \n", "5995        {'ground_truth': '1242_6', 'style': 'rule'}   \n", "5996             {'ground_truth': '2', 'style': 'rule'}   \n", "5997       {'ground_truth': 'n = 255', 'style': 'rule'}   \n", "5998             {'ground_truth': '8', 'style': 'rule'}   \n", "5999   {'ground_truth': '\\frac{1}{8}', 'style': 'rule'}   \n", "\n", "                             extra_info         cluster  accuracy  \\\n", "0     {'index': 4664, 'split': 'train'}  math_cluster_0       0.0   \n", "1     {'index': 4411, 'split': 'train'}  math_cluster_7       0.2   \n", "2     {'index': 7448, 'split': 'train'}  math_cluster_9       0.0   \n", "3     {'index': 1919, 'split': 'train'}  math_cluster_0       0.0   \n", "4     {'index': 1298, 'split': 'train'}  math_cluster_1       0.1   \n", "...                                 ...             ...       ...   \n", "5995  {'index': 5191, 'split': 'train'}  math_cluster_1       0.2   \n", "5996  {'index': 5226, 'split': 'train'}  math_cluster_4       0.8   \n", "5997  {'index': 5390, 'split': 'train'}  math_cluster_0       0.1   \n", "5998   {'index': 860, 'split': 'train'}  math_cluster_3       0.2   \n", "5999  {'index': 7270, 'split': 'train'}  math_cluster_3       0.3   \n", "\n", "        accuracy_bin  \n", "0     gsm8k_abin_0.0  \n", "1     gsm8k_abin_0.2  \n", "2     gsm8k_abin_0.0  \n", "3     gsm8k_abin_0.0  \n", "4     gsm8k_abin_0.1  \n", "...              ...  \n", "5995  gsm8k_abin_0.2  \n", "5996  gsm8k_abin_0.8  \n", "5997  gsm8k_abin_0.1  \n", "5998  gsm8k_abin_0.2  \n", "5999  gsm8k_abin_0.3  \n", "\n", "[6000 rows x 10 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["df_merged['accuracy_bin'] = df_merged['accuracy'].apply(lambda x: f\"gsm8k_abin_{x}\")\n", "df_merged"]}, {"cell_type": "code", "execution_count": 9, "id": "57ce3797", "metadata": {}, "outputs": [], "source": ["df_merged.to_parquet(\"/data/yzr/DUMP/examples/data/math/train.parquet\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}