{"cells": [{"cell_type": "code", "execution_count": 1, "id": "12f6fe90", "metadata": {}, "outputs": [], "source": ["# FILE_FOLDER = \"~/data/gsm8k_d/train_harder.parquet\"\n", "FILE_FOLDER1 = \"~/ne-data/gsm8k/train.parquet\"\n", "FILE_FOLDER2 = \"/data/yzr/DUMP/examples/data/gsm8k-train-extracted-rate.parquet\"\n", "\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "41a12a0f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data_source</th>\n", "      <th>prompt</th>\n", "      <th>ability</th>\n", "      <th>reward_model</th>\n", "      <th>extra_info</th>\n", "      <th>cluster</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON><PERSON> sells bonsai. A small bons...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '190', 'style': 'rule'}</td>\n", "      <td>{'answer': '<PERSON><PERSON> earns $30 x 3 = $&lt;&lt;30*3=90&gt;&gt;...</td>\n", "      <td>gsm8k_cluster_6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'There are 6 times as many lab co...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '14', 'style': 'rule'}</td>\n", "      <td>{'answer': 'If there are 12 uniforms, and the ...</td>\n", "      <td>gsm8k_cluster_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'At school today, <PERSON><PERSON><PERSON> was 20...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '140', 'style': 'rule'}</td>\n", "      <td>{'answer': 'If <PERSON><PERSON><PERSON> was 20 minutes late, e...</td>\n", "      <td>gsm8k_cluster_2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> has 18 goldfish. Each wee...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '4', 'style': 'rule'}</td>\n", "      <td>{'answer': 'In 7 weeks 5*7 = &lt;&lt;35=35&gt;&gt;35 goldf...</td>\n", "      <td>gsm8k_cluster_7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> is drawing pictures. She ha...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '10', 'style': 'rule'}</td>\n", "      <td>{'answer': 'She can make 18 drawings in total ...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5973</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> works in a factory and every...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '480', 'style': 'rule'}</td>\n", "      <td>{'answer': 'Since there are 2 sets of 30 minut...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5974</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> gets prize points every t...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '850', 'style': 'rule'}</td>\n", "      <td>{'answer': 'First find the total cost of the b...</td>\n", "      <td>gsm8k_cluster_5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5975</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'Several birds were sitting in th...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '18', 'style': 'rule'}</td>\n", "      <td>{'answer': 'With half as many swallows as blue...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5976</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> likes to collect model train...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '45', 'style': 'rule'}</td>\n", "      <td>{'answer': '<PERSON> gets 1+2=&lt;&lt;1+2=3&gt;&gt;3 trains per...</td>\n", "      <td>gsm8k_cluster_0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5977</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> waits on a customer whose ...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '2', 'style': 'rule'}</td>\n", "      <td>{'answer': 'First calculate how much the tax i...</td>\n", "      <td>gsm8k_cluster_7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5978 rows × 6 columns</p>\n", "</div>"], "text/plain": ["       data_source                                             prompt ability  \\\n", "0     openai/gsm8k  [{'content': '<PERSON>ry sells bonsai. A small bons...    math   \n", "1     openai/gsm8k  [{'content': 'There are 6 times as many lab co...    math   \n", "2     openai/gsm8k  [{'content': 'At school today, <PERSON><PERSON><PERSON> was 20...    math   \n", "3     openai/gsm8k  [{'content': '<PERSON> has 18 goldfish. Each wee...    math   \n", "4     openai/gsm8k  [{'content': '<PERSON> is drawing pictures. She ha...    math   \n", "...            ...                                                ...     ...   \n", "5973  openai/gsm8k  [{'content': '<PERSON> works in a factory and every...    math   \n", "5974  openai/gsm8k  [{'content': '<PERSON> gets prize points every t...    math   \n", "5975  openai/gsm8k  [{'content': 'Several birds were sitting in th...    math   \n", "5976  openai/gsm8k  [{'content': '<PERSON> likes to collect model train...    math   \n", "5977  openai/gsm8k  [{'content': '<PERSON> waits on a customer whose ...    math   \n", "\n", "                                  reward_model  \\\n", "0     {'ground_truth': '190', 'style': 'rule'}   \n", "1      {'ground_truth': '14', 'style': 'rule'}   \n", "2     {'ground_truth': '140', 'style': 'rule'}   \n", "3       {'ground_truth': '4', 'style': 'rule'}   \n", "4      {'ground_truth': '10', 'style': 'rule'}   \n", "...                                        ...   \n", "5973  {'ground_truth': '480', 'style': 'rule'}   \n", "5974  {'ground_truth': '850', 'style': 'rule'}   \n", "5975   {'ground_truth': '18', 'style': 'rule'}   \n", "5976   {'ground_truth': '45', 'style': 'rule'}   \n", "5977    {'ground_truth': '2', 'style': 'rule'}   \n", "\n", "                                             extra_info          cluster  \n", "0     {'answer': '<PERSON><PERSON> earns $30 x 3 = $<<30*3=90>>...  gsm8k_cluster_6  \n", "1     {'answer': 'If there are 12 uniforms, and the ...  gsm8k_cluster_0  \n", "2     {'answer': 'If <PERSON><PERSON><PERSON> was 20 minutes late, e...  gsm8k_cluster_2  \n", "3     {'answer': 'In 7 weeks 5*7 = <<35=35>>35 goldf...  gsm8k_cluster_7  \n", "4     {'answer': 'She can make 18 drawings in total ...  gsm8k_cluster_9  \n", "...                                                 ...              ...  \n", "5973  {'answer': 'Since there are 2 sets of 30 minut...  gsm8k_cluster_9  \n", "5974  {'answer': 'First find the total cost of the b...  gsm8k_cluster_5  \n", "5975  {'answer': 'With half as many swallows as blue...  gsm8k_cluster_9  \n", "5976  {'answer': '<PERSON> gets 1+2=<<1+2=3>>3 trains per...  gsm8k_cluster_0  \n", "5977  {'answer': 'First calculate how much the tax i...  gsm8k_cluster_7  \n", "\n", "[5978 rows x 6 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df1 = pd.read_parquet(FILE_FOLDER1)\n", "df1"]}, {"cell_type": "code", "execution_count": 3, "id": "ddf8ae65", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>question</th>\n", "      <th>answer</th>\n", "      <th>answer-extracted</th>\n", "      <th>accuracy</th>\n", "      <th>k_repetitions</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON> sold clips to 48 of her friends in Apr...</td>\n", "      <td>Natalia sold 48/2 = &lt;&lt;48/2=24&gt;&gt;24 clips in May...</td>\n", "      <td>72</td>\n", "      <td>0.2</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON> earns $12 an hour for babysitting. Yester...</td>\n", "      <td><PERSON><PERSON> earns 12/60 = $&lt;&lt;12/60=0.2&gt;&gt;0.2 per minut...</td>\n", "      <td>10</td>\n", "      <td>0.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON> is saving money for a new wallet which c...</td>\n", "      <td>In the beginning, <PERSON> has only 100 / 2 = $&lt;&lt;...</td>\n", "      <td>5</td>\n", "      <td>0.7</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON> is reading a 120-page book. Yesterday, s...</td>\n", "      <td>Maila read 12 x 2 = &lt;&lt;12*2=24&gt;&gt;24 pages today....</td>\n", "      <td>42</td>\n", "      <td>0.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON> writes a 3-page letter to 2 different fr...</td>\n", "      <td>He writes each friend 3*2=&lt;&lt;3*2=6&gt;&gt;6 pages a w...</td>\n", "      <td>624</td>\n", "      <td>0.1</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7468</th>\n", "      <td>Very early this morning, <PERSON> left home in a ...</td>\n", "      <td>For the distance she traveled, <PERSON> paid 23 -...</td>\n", "      <td>5</td>\n", "      <td>0.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7469</th>\n", "      <td><PERSON> is saving up for a box of cookies. To rai...</td>\n", "      <td>He makes $.5 profit on each bracelet because 1...</td>\n", "      <td>3</td>\n", "      <td>0.3</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7470</th>\n", "      <td><PERSON> can skip at six times the speed that Bra...</td>\n", "      <td><PERSON> can skip at twice the speed that <PERSON> ca...</td>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7471</th>\n", "      <td><PERSON>, a third grade teacher, is picking up th...</td>\n", "      <td><PERSON> needs 35 lunches for the kids + 5 for th...</td>\n", "      <td>308</td>\n", "      <td>0.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7472</th>\n", "      <td>At 30, <PERSON><PERSON> is 4/3 the age of <PERSON>. What wo...</td>\n", "      <td>If <PERSON><PERSON> is 30 now, in 15 years, she'll be 30+...</td>\n", "      <td>50</td>\n", "      <td>0.0</td>\n", "      <td>10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7473 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                               question  \\\n", "0     <PERSON> sold clips to 48 of her friends in Apr...   \n", "1     Weng earns $12 an hour for babysitting. Yester...   \n", "2     <PERSON> is saving money for a new wallet which c...   \n", "3     <PERSON> is reading a 120-page book. Yesterday, s...   \n", "4     <PERSON> writes a 3-page letter to 2 different fr...   \n", "...                                                 ...   \n", "7468  Very early this morning, <PERSON> left home in a ...   \n", "7469  <PERSON> is saving up for a box of cookies. To rai...   \n", "7470  Colin can skip at six times the speed that Bra...   \n", "7471  <PERSON>, a third grade teacher, is picking up th...   \n", "7472  At 30, <PERSON><PERSON> is 4/3 the age of <PERSON>. What wo...   \n", "\n", "                                                 answer answer-extracted  \\\n", "0     Natalia sold 48/2 = <<48/2=24>>24 clips in May...               72   \n", "1     <PERSON>g earns 12/60 = $<<12/60=0.2>>0.2 per minut...               10   \n", "2     In the beginning, <PERSON> has only 100 / 2 = $<<...                5   \n", "3     Mail<PERSON> read 12 x 2 = <<12*2=24>>24 pages today....               42   \n", "4     He writes each friend 3*2=<<3*2=6>>6 pages a w...              624   \n", "...                                                 ...              ...   \n", "7468  For the distance she traveled, <PERSON> paid 23 -...                5   \n", "7469  He makes $.5 profit on each bracelet because 1...                3   \n", "7470  Tony can skip at twice the speed that <PERSON> ca...                4   \n", "7471  <PERSON> needs 35 lunches for the kids + 5 for th...              308   \n", "7472  If <PERSON><PERSON> is 30 now, in 15 years, she'll be 30+...               50   \n", "\n", "      accuracy  k_repetitions  \n", "0          0.2             10  \n", "1          0.0             10  \n", "2          0.7             10  \n", "3          0.0             10  \n", "4          0.1             10  \n", "...        ...            ...  \n", "7468       0.0             10  \n", "7469       0.3             10  \n", "7470       0.0             10  \n", "7471       0.0             10  \n", "7472       0.0             10  \n", "\n", "[7473 rows x 5 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df2 =  pd.read_parquet(FILE_FOLDER2)\n", "df2"]}, {"cell_type": "code", "execution_count": 4, "id": "a8372792", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data_source</th>\n", "      <th>prompt</th>\n", "      <th>ability</th>\n", "      <th>reward_model</th>\n", "      <th>extra_info</th>\n", "      <th>cluster</th>\n", "      <th>question_x</th>\n", "      <th>matched_problem</th>\n", "      <th>match_score</th>\n", "      <th>question_y</th>\n", "      <th>accuracy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON><PERSON> sells bonsai. A small bons...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '190', 'style': 'rule'}</td>\n", "      <td>{'answer': '<PERSON><PERSON> earns $30 x 3 = $&lt;&lt;30*3=90&gt;&gt;...</td>\n", "      <td>gsm8k_cluster_6</td>\n", "      <td><PERSON><PERSON> sells bonsai. A small bonsai costs $30 a...</td>\n", "      <td><PERSON><PERSON> sells bonsai. A small bonsai costs $30 a...</td>\n", "      <td>80.466472</td>\n", "      <td><PERSON><PERSON> sells bonsai. A small bonsai costs $30 a...</td>\n", "      <td>0.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'There are 6 times as many lab co...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '14', 'style': 'rule'}</td>\n", "      <td>{'answer': 'If there are 12 uniforms, and the ...</td>\n", "      <td>gsm8k_cluster_0</td>\n", "      <td>There are 6 times as many lab coats as uniform...</td>\n", "      <td>There are 6 times as many lab coats as uniform...</td>\n", "      <td>89.415482</td>\n", "      <td>There are 6 times as many lab coats as uniform...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'At school today, <PERSON><PERSON><PERSON> was 20...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '140', 'style': 'rule'}</td>\n", "      <td>{'answer': 'If <PERSON><PERSON><PERSON> was 20 minutes late, e...</td>\n", "      <td>gsm8k_cluster_2</td>\n", "      <td>At school today, <PERSON><PERSON><PERSON> was 20 minutes late....</td>\n", "      <td>At school today, <PERSON><PERSON><PERSON> was 20 minutes late....</td>\n", "      <td>83.618582</td>\n", "      <td>At school today, <PERSON><PERSON><PERSON> was 20 minutes late....</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> has 18 goldfish. Each wee...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '4', 'style': 'rule'}</td>\n", "      <td>{'answer': 'In 7 weeks 5*7 = &lt;&lt;35=35&gt;&gt;35 goldf...</td>\n", "      <td>gsm8k_cluster_7</td>\n", "      <td><PERSON> has 18 goldfish. Each week 5 goldfish d...</td>\n", "      <td><PERSON> has 18 goldfish. Each week 5 goldfish d...</td>\n", "      <td>80.691643</td>\n", "      <td><PERSON> has 18 goldfish. Each week 5 goldfish d...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> is drawing pictures. She ha...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '10', 'style': 'rule'}</td>\n", "      <td>{'answer': 'She can make 18 drawings in total ...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "      <td><PERSON> is drawing pictures. She has 12 markers a...</td>\n", "      <td><PERSON> is drawing pictures. She has 12 markers a...</td>\n", "      <td>85.591398</td>\n", "      <td><PERSON> is drawing pictures. She has 12 markers a...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5973</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> works in a factory and every...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '480', 'style': 'rule'}</td>\n", "      <td>{'answer': 'Since there are 2 sets of 30 minut...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "      <td>Sue works in a factory and every 30 minutes, a...</td>\n", "      <td>Sue works in a factory and every 30 minutes, a...</td>\n", "      <td>81.842818</td>\n", "      <td>Sue works in a factory and every 30 minutes, a...</td>\n", "      <td>0.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5974</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> gets prize points every t...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '850', 'style': 'rule'}</td>\n", "      <td>{'answer': 'First find the total cost of the b...</td>\n", "      <td>gsm8k_cluster_5</td>\n", "      <td><PERSON> gets prize points every time she shops ...</td>\n", "      <td><PERSON> gets prize points every time she shops ...</td>\n", "      <td>91.218873</td>\n", "      <td><PERSON> gets prize points every time she shops ...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5975</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'Several birds were sitting in th...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '18', 'style': 'rule'}</td>\n", "      <td>{'answer': 'With half as many swallows as blue...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "      <td>Several birds were sitting in the branches of ...</td>\n", "      <td>Several birds were sitting in the branches of ...</td>\n", "      <td>88.057041</td>\n", "      <td>Several birds were sitting in the branches of ...</td>\n", "      <td>0.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5976</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> likes to collect model train...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '45', 'style': 'rule'}</td>\n", "      <td>{'answer': '<PERSON> gets 1+2=&lt;&lt;1+2=3&gt;&gt;3 trains per...</td>\n", "      <td>gsm8k_cluster_0</td>\n", "      <td><PERSON> likes to collect model trains.  He asks fo...</td>\n", "      <td><PERSON> likes to collect model trains.  He asks fo...</td>\n", "      <td>90.884354</td>\n", "      <td><PERSON> likes to collect model trains.  He asks fo...</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5977</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> waits on a customer whose ...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '2', 'style': 'rule'}</td>\n", "      <td>{'answer': 'First calculate how much the tax i...</td>\n", "      <td>gsm8k_cluster_7</td>\n", "      <td><PERSON> waits on a customer whose check comes to...</td>\n", "      <td><PERSON> waits on a customer whose check comes to...</td>\n", "      <td>85.953878</td>\n", "      <td><PERSON> waits on a customer whose check comes to...</td>\n", "      <td>0.3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5978 rows × 11 columns</p>\n", "</div>"], "text/plain": ["       data_source                                             prompt ability  \\\n", "0     openai/gsm8k  [{'content': '<PERSON>ry sells bonsai. A small bons...    math   \n", "1     openai/gsm8k  [{'content': 'There are 6 times as many lab co...    math   \n", "2     openai/gsm8k  [{'content': 'At school today, <PERSON><PERSON><PERSON> was 20...    math   \n", "3     openai/gsm8k  [{'content': '<PERSON> has 18 goldfish. Each wee...    math   \n", "4     openai/gsm8k  [{'content': '<PERSON> is drawing pictures. She ha...    math   \n", "...            ...                                                ...     ...   \n", "5973  openai/gsm8k  [{'content': '<PERSON> works in a factory and every...    math   \n", "5974  openai/gsm8k  [{'content': '<PERSON> gets prize points every t...    math   \n", "5975  openai/gsm8k  [{'content': 'Several birds were sitting in th...    math   \n", "5976  openai/gsm8k  [{'content': '<PERSON> likes to collect model train...    math   \n", "5977  openai/gsm8k  [{'content': '<PERSON> waits on a customer whose ...    math   \n", "\n", "                                  reward_model  \\\n", "0     {'ground_truth': '190', 'style': 'rule'}   \n", "1      {'ground_truth': '14', 'style': 'rule'}   \n", "2     {'ground_truth': '140', 'style': 'rule'}   \n", "3       {'ground_truth': '4', 'style': 'rule'}   \n", "4      {'ground_truth': '10', 'style': 'rule'}   \n", "...                                        ...   \n", "5973  {'ground_truth': '480', 'style': 'rule'}   \n", "5974  {'ground_truth': '850', 'style': 'rule'}   \n", "5975   {'ground_truth': '18', 'style': 'rule'}   \n", "5976   {'ground_truth': '45', 'style': 'rule'}   \n", "5977    {'ground_truth': '2', 'style': 'rule'}   \n", "\n", "                                             extra_info          cluster  \\\n", "0     {'answer': '<PERSON><PERSON> earns $30 x 3 = $<<30*3=90>>...  gsm8k_cluster_6   \n", "1     {'answer': 'If there are 12 uniforms, and the ...  gsm8k_cluster_0   \n", "2     {'answer': 'If <PERSON><PERSON><PERSON> was 20 minutes late, e...  gsm8k_cluster_2   \n", "3     {'answer': 'In 7 weeks 5*7 = <<35=35>>35 goldf...  gsm8k_cluster_7   \n", "4     {'answer': 'She can make 18 drawings in total ...  gsm8k_cluster_9   \n", "...                                                 ...              ...   \n", "5973  {'answer': 'Since there are 2 sets of 30 minut...  gsm8k_cluster_9   \n", "5974  {'answer': 'First find the total cost of the b...  gsm8k_cluster_5   \n", "5975  {'answer': 'With half as many swallows as blue...  gsm8k_cluster_9   \n", "5976  {'answer': '<PERSON> gets 1+2=<<1+2=3>>3 trains per...  gsm8k_cluster_0   \n", "5977  {'answer': 'First calculate how much the tax i...  gsm8k_cluster_7   \n", "\n", "                                             question_x  \\\n", "0     Lowry sells bonsai. A small bonsai costs $30 a...   \n", "1     There are 6 times as many lab coats as uniform...   \n", "2     At school today, <PERSON><PERSON><PERSON> was 20 minutes late....   \n", "3     Martin has 18 goldfish. Each week 5 goldfish d...   \n", "4     <PERSON> is drawing pictures. She has 12 markers a...   \n", "...                                                 ...   \n", "5973  <PERSON> works in a factory and every 30 minutes, a...   \n", "5974  <PERSON> gets prize points every time she shops ...   \n", "5975  Several birds were sitting in the branches of ...   \n", "5976  <PERSON> likes to collect model trains.  He asks fo...   \n", "5977  <PERSON> waits on a customer whose check comes to...   \n", "\n", "                                        matched_problem  match_score  \\\n", "0     Lowry sells bonsai. A small bonsai costs $30 a...    80.466472   \n", "1     There are 6 times as many lab coats as uniform...    89.415482   \n", "2     At school today, <PERSON><PERSON><PERSON> was 20 minutes late....    83.618582   \n", "3     Martin has 18 goldfish. Each week 5 goldfish d...    80.691643   \n", "4     <PERSON> is drawing pictures. She has 12 markers a...    85.591398   \n", "...                                                 ...          ...   \n", "5973  <PERSON> works in a factory and every 30 minutes, a...    81.842818   \n", "5974  <PERSON> gets prize points every time she shops ...    91.218873   \n", "5975  Several birds were sitting in the branches of ...    88.057041   \n", "5976  <PERSON> likes to collect model trains.  He asks fo...    90.884354   \n", "5977  <PERSON> waits on a customer whose check comes to...    85.953878   \n", "\n", "                                             question_y  accuracy  \n", "0     Lowry sells bonsai. A small bonsai costs $30 a...       0.6  \n", "1     There are 6 times as many lab coats as uniform...       0.0  \n", "2     At school today, <PERSON><PERSON><PERSON> was 20 minutes late....       0.0  \n", "3     Martin has 18 goldfish. Each week 5 goldfish d...       0.0  \n", "4     <PERSON> is drawing pictures. She has 12 markers a...       0.0  \n", "...                                                 ...       ...  \n", "5973  Sue works in a factory and every 30 minutes, a...       0.4  \n", "5974  <PERSON> gets prize points every time she shops ...       0.0  \n", "5975  Several birds were sitting in the branches of ...       0.5  \n", "5976  <PERSON> likes to collect model trains.  He asks fo...       0.0  \n", "5977  <PERSON> waits on a customer whose check comes to...       0.3  \n", "\n", "[5978 rows x 11 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# prompt 是list，且第一个元素的content就是question\n", "df1['question'] = df1['prompt'].apply(lambda x: x[0]['content'])\n", "\n", "from rapidfuzz import process, fuzz\n", "\n", "# 假设df1['question']和df2['problem']都已准备好\n", "\n", "def match_problem(question, problems, scorer=fuzz.ratio):\n", "    # 取分数最高的匹配\n", "    match, score, idx = process.extractOne(question, problems, scorer=scorer)\n", "    return match, score\n", "\n", "# 为df1找出最相似的problem\n", "matches = df1['question'].apply(\n", "    lambda q: match_problem(q, df2['question'].tolist())\n", ")\n", "\n", "df1['matched_problem'] = matches.apply(lambda x: x[0])\n", "df1['match_score'] = matches.apply(lambda x: x[1])\n", "\n", "# 通过matched_problem合并accuracy\n", "df_merged = df1.merge(df2[['question', 'accuracy']], left_on='matched_problem', right_on='question', how='left')\n", "df_merged"]}, {"cell_type": "code", "execution_count": 5, "id": "d74b8d0b", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data_source</th>\n", "      <th>prompt</th>\n", "      <th>ability</th>\n", "      <th>reward_model</th>\n", "      <th>extra_info</th>\n", "      <th>cluster</th>\n", "      <th>accuracy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON><PERSON> sells bonsai. A small bons...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '190', 'style': 'rule'}</td>\n", "      <td>{'answer': '<PERSON><PERSON> earns $30 x 3 = $&lt;&lt;30*3=90&gt;&gt;...</td>\n", "      <td>gsm8k_cluster_6</td>\n", "      <td>0.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'There are 6 times as many lab co...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '14', 'style': 'rule'}</td>\n", "      <td>{'answer': 'If there are 12 uniforms, and the ...</td>\n", "      <td>gsm8k_cluster_0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'At school today, <PERSON><PERSON><PERSON> was 20...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '140', 'style': 'rule'}</td>\n", "      <td>{'answer': 'If <PERSON><PERSON><PERSON> was 20 minutes late, e...</td>\n", "      <td>gsm8k_cluster_2</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> has 18 goldfish. Each wee...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '4', 'style': 'rule'}</td>\n", "      <td>{'answer': 'In 7 weeks 5*7 = &lt;&lt;35=35&gt;&gt;35 goldf...</td>\n", "      <td>gsm8k_cluster_7</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> is drawing pictures. She ha...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '10', 'style': 'rule'}</td>\n", "      <td>{'answer': 'She can make 18 drawings in total ...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5973</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> works in a factory and every...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '480', 'style': 'rule'}</td>\n", "      <td>{'answer': 'Since there are 2 sets of 30 minut...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "      <td>0.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5974</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> gets prize points every t...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '850', 'style': 'rule'}</td>\n", "      <td>{'answer': 'First find the total cost of the b...</td>\n", "      <td>gsm8k_cluster_5</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5975</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'Several birds were sitting in th...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '18', 'style': 'rule'}</td>\n", "      <td>{'answer': 'With half as many swallows as blue...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "      <td>0.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5976</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> likes to collect model train...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '45', 'style': 'rule'}</td>\n", "      <td>{'answer': '<PERSON> gets 1+2=&lt;&lt;1+2=3&gt;&gt;3 trains per...</td>\n", "      <td>gsm8k_cluster_0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5977</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> waits on a customer whose ...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '2', 'style': 'rule'}</td>\n", "      <td>{'answer': 'First calculate how much the tax i...</td>\n", "      <td>gsm8k_cluster_7</td>\n", "      <td>0.3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5978 rows × 7 columns</p>\n", "</div>"], "text/plain": ["       data_source                                             prompt ability  \\\n", "0     openai/gsm8k  [{'content': '<PERSON>ry sells bonsai. A small bons...    math   \n", "1     openai/gsm8k  [{'content': 'There are 6 times as many lab co...    math   \n", "2     openai/gsm8k  [{'content': 'At school today, <PERSON><PERSON><PERSON> was 20...    math   \n", "3     openai/gsm8k  [{'content': '<PERSON> has 18 goldfish. Each wee...    math   \n", "4     openai/gsm8k  [{'content': '<PERSON> is drawing pictures. She ha...    math   \n", "...            ...                                                ...     ...   \n", "5973  openai/gsm8k  [{'content': '<PERSON> works in a factory and every...    math   \n", "5974  openai/gsm8k  [{'content': '<PERSON> gets prize points every t...    math   \n", "5975  openai/gsm8k  [{'content': 'Several birds were sitting in th...    math   \n", "5976  openai/gsm8k  [{'content': '<PERSON> likes to collect model train...    math   \n", "5977  openai/gsm8k  [{'content': '<PERSON> waits on a customer whose ...    math   \n", "\n", "                                  reward_model  \\\n", "0     {'ground_truth': '190', 'style': 'rule'}   \n", "1      {'ground_truth': '14', 'style': 'rule'}   \n", "2     {'ground_truth': '140', 'style': 'rule'}   \n", "3       {'ground_truth': '4', 'style': 'rule'}   \n", "4      {'ground_truth': '10', 'style': 'rule'}   \n", "...                                        ...   \n", "5973  {'ground_truth': '480', 'style': 'rule'}   \n", "5974  {'ground_truth': '850', 'style': 'rule'}   \n", "5975   {'ground_truth': '18', 'style': 'rule'}   \n", "5976   {'ground_truth': '45', 'style': 'rule'}   \n", "5977    {'ground_truth': '2', 'style': 'rule'}   \n", "\n", "                                             extra_info          cluster  \\\n", "0     {'answer': '<PERSON><PERSON> earns $30 x 3 = $<<30*3=90>>...  gsm8k_cluster_6   \n", "1     {'answer': 'If there are 12 uniforms, and the ...  gsm8k_cluster_0   \n", "2     {'answer': 'If <PERSON><PERSON><PERSON> was 20 minutes late, e...  gsm8k_cluster_2   \n", "3     {'answer': 'In 7 weeks 5*7 = <<35=35>>35 goldf...  gsm8k_cluster_7   \n", "4     {'answer': 'She can make 18 drawings in total ...  gsm8k_cluster_9   \n", "...                                                 ...              ...   \n", "5973  {'answer': 'Since there are 2 sets of 30 minut...  gsm8k_cluster_9   \n", "5974  {'answer': 'First find the total cost of the b...  gsm8k_cluster_5   \n", "5975  {'answer': 'With half as many swallows as blue...  gsm8k_cluster_9   \n", "5976  {'answer': '<PERSON> gets 1+2=<<1+2=3>>3 trains per...  gsm8k_cluster_0   \n", "5977  {'answer': 'First calculate how much the tax i...  gsm8k_cluster_7   \n", "\n", "      accuracy  \n", "0          0.6  \n", "1          0.0  \n", "2          0.0  \n", "3          0.0  \n", "4          0.0  \n", "...        ...  \n", "5973       0.4  \n", "5974       0.0  \n", "5975       0.5  \n", "5976       0.0  \n", "5977       0.3  \n", "\n", "[5978 rows x 7 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_merged = df_merged.drop(columns=['question_x','matched_problem','match_score','question_y'])\n", "df_merged"]}, {"cell_type": "code", "execution_count": 11, "id": "a90551bc", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>data_source</th>\n", "      <th>prompt</th>\n", "      <th>ability</th>\n", "      <th>reward_model</th>\n", "      <th>extra_info</th>\n", "      <th>cluster</th>\n", "      <th>accuracy</th>\n", "      <th>accuracy_bin</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON><PERSON> sells bonsai. A small bons...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '190', 'style': 'rule'}</td>\n", "      <td>{'answer': '<PERSON><PERSON> earns $30 x 3 = $&lt;&lt;30*3=90&gt;&gt;...</td>\n", "      <td>gsm8k_cluster_6</td>\n", "      <td>0.6</td>\n", "      <td>gsm8k_abin_0.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'There are 6 times as many lab co...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '14', 'style': 'rule'}</td>\n", "      <td>{'answer': 'If there are 12 uniforms, and the ...</td>\n", "      <td>gsm8k_cluster_0</td>\n", "      <td>0.0</td>\n", "      <td>gsm8k_abin_0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'At school today, <PERSON><PERSON><PERSON> was 20...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '140', 'style': 'rule'}</td>\n", "      <td>{'answer': 'If <PERSON><PERSON><PERSON> was 20 minutes late, e...</td>\n", "      <td>gsm8k_cluster_2</td>\n", "      <td>0.0</td>\n", "      <td>gsm8k_abin_0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> has 18 goldfish. Each wee...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '4', 'style': 'rule'}</td>\n", "      <td>{'answer': 'In 7 weeks 5*7 = &lt;&lt;35=35&gt;&gt;35 goldf...</td>\n", "      <td>gsm8k_cluster_7</td>\n", "      <td>0.0</td>\n", "      <td>gsm8k_abin_0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> is drawing pictures. She ha...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '10', 'style': 'rule'}</td>\n", "      <td>{'answer': 'She can make 18 drawings in total ...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "      <td>0.0</td>\n", "      <td>gsm8k_abin_0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5973</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> works in a factory and every...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '480', 'style': 'rule'}</td>\n", "      <td>{'answer': 'Since there are 2 sets of 30 minut...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "      <td>0.4</td>\n", "      <td>gsm8k_abin_0.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5974</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> gets prize points every t...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '850', 'style': 'rule'}</td>\n", "      <td>{'answer': 'First find the total cost of the b...</td>\n", "      <td>gsm8k_cluster_5</td>\n", "      <td>0.0</td>\n", "      <td>gsm8k_abin_0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5975</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': 'Several birds were sitting in th...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '18', 'style': 'rule'}</td>\n", "      <td>{'answer': 'With half as many swallows as blue...</td>\n", "      <td>gsm8k_cluster_9</td>\n", "      <td>0.5</td>\n", "      <td>gsm8k_abin_0.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5976</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> likes to collect model train...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '45', 'style': 'rule'}</td>\n", "      <td>{'answer': '<PERSON> gets 1+2=&lt;&lt;1+2=3&gt;&gt;3 trains per...</td>\n", "      <td>gsm8k_cluster_0</td>\n", "      <td>0.0</td>\n", "      <td>gsm8k_abin_0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5977</th>\n", "      <td>openai/gsm8k</td>\n", "      <td>[{'content': '<PERSON> waits on a customer whose ...</td>\n", "      <td>math</td>\n", "      <td>{'ground_truth': '2', 'style': 'rule'}</td>\n", "      <td>{'answer': 'First calculate how much the tax i...</td>\n", "      <td>gsm8k_cluster_7</td>\n", "      <td>0.3</td>\n", "      <td>gsm8k_abin_0.3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5978 rows × 8 columns</p>\n", "</div>"], "text/plain": ["       data_source                                             prompt ability  \\\n", "0     openai/gsm8k  [{'content': '<PERSON>ry sells bonsai. A small bons...    math   \n", "1     openai/gsm8k  [{'content': 'There are 6 times as many lab co...    math   \n", "2     openai/gsm8k  [{'content': 'At school today, <PERSON><PERSON><PERSON> was 20...    math   \n", "3     openai/gsm8k  [{'content': '<PERSON> has 18 goldfish. Each wee...    math   \n", "4     openai/gsm8k  [{'content': '<PERSON> is drawing pictures. She ha...    math   \n", "...            ...                                                ...     ...   \n", "5973  openai/gsm8k  [{'content': '<PERSON> works in a factory and every...    math   \n", "5974  openai/gsm8k  [{'content': '<PERSON> gets prize points every t...    math   \n", "5975  openai/gsm8k  [{'content': 'Several birds were sitting in th...    math   \n", "5976  openai/gsm8k  [{'content': '<PERSON> likes to collect model train...    math   \n", "5977  openai/gsm8k  [{'content': '<PERSON> waits on a customer whose ...    math   \n", "\n", "                                  reward_model  \\\n", "0     {'ground_truth': '190', 'style': 'rule'}   \n", "1      {'ground_truth': '14', 'style': 'rule'}   \n", "2     {'ground_truth': '140', 'style': 'rule'}   \n", "3       {'ground_truth': '4', 'style': 'rule'}   \n", "4      {'ground_truth': '10', 'style': 'rule'}   \n", "...                                        ...   \n", "5973  {'ground_truth': '480', 'style': 'rule'}   \n", "5974  {'ground_truth': '850', 'style': 'rule'}   \n", "5975   {'ground_truth': '18', 'style': 'rule'}   \n", "5976   {'ground_truth': '45', 'style': 'rule'}   \n", "5977    {'ground_truth': '2', 'style': 'rule'}   \n", "\n", "                                             extra_info          cluster  \\\n", "0     {'answer': '<PERSON><PERSON> earns $30 x 3 = $<<30*3=90>>...  gsm8k_cluster_6   \n", "1     {'answer': 'If there are 12 uniforms, and the ...  gsm8k_cluster_0   \n", "2     {'answer': 'If <PERSON><PERSON><PERSON> was 20 minutes late, e...  gsm8k_cluster_2   \n", "3     {'answer': 'In 7 weeks 5*7 = <<35=35>>35 goldf...  gsm8k_cluster_7   \n", "4     {'answer': 'She can make 18 drawings in total ...  gsm8k_cluster_9   \n", "...                                                 ...              ...   \n", "5973  {'answer': 'Since there are 2 sets of 30 minut...  gsm8k_cluster_9   \n", "5974  {'answer': 'First find the total cost of the b...  gsm8k_cluster_5   \n", "5975  {'answer': 'With half as many swallows as blue...  gsm8k_cluster_9   \n", "5976  {'answer': '<PERSON> gets 1+2=<<1+2=3>>3 trains per...  gsm8k_cluster_0   \n", "5977  {'answer': 'First calculate how much the tax i...  gsm8k_cluster_7   \n", "\n", "      accuracy    accuracy_bin  \n", "0          0.6  gsm8k_abin_0.6  \n", "1          0.0  gsm8k_abin_0.0  \n", "2          0.0  gsm8k_abin_0.0  \n", "3          0.0  gsm8k_abin_0.0  \n", "4          0.0  gsm8k_abin_0.0  \n", "...        ...             ...  \n", "5973       0.4  gsm8k_abin_0.4  \n", "5974       0.0  gsm8k_abin_0.0  \n", "5975       0.5  gsm8k_abin_0.5  \n", "5976       0.0  gsm8k_abin_0.0  \n", "5977       0.3  gsm8k_abin_0.3  \n", "\n", "[5978 rows x 8 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df_merged['accuracy_bin'] = df_merged['accuracy'].apply(lambda x: f\"gsm8k_abin_{x}\")\n", "df_merged"]}, {"cell_type": "code", "execution_count": 12, "id": "57ce3797", "metadata": {}, "outputs": [], "source": ["df_merged.to_parquet(\"/data/yzr/DUMP/examples/data/gsm8k/train.parquet\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}