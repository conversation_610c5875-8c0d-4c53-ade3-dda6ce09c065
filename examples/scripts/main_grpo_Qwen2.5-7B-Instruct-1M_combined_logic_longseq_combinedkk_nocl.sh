set -x
MODEL_PATH=Qwen/Qwen2.5-7B-Instruct-1M
export VLLM_ATTENTION_BACKEND=XFORMERS

max_prompt_length=$((1024 * 4))
max_response_length=$((1024 * 20))

use_dynamic_bsz=True
actor_ppo_max_token_len=8000
infer_ppo_max_token_len=8000
offload=True
gen_tp=1
sp_size=4

train_batch_size=128
val_batch_size=$((train_batch_size * 3))

python3 -m verl.trainer.main_ppo \
    algorithm.adv_estimator=grpo \
    data.train_files=./combined_logic_dataset/generate_combined_kk/combined_logic_datasets_train.parquet \
    data.val_files=./combined_logic_dataset/generate_combined_kk/combined_logic_datasets_test.parquet \
    data.train_batch_size=${train_batch_size} \
    data.val_batch_size=${val_batch_size} \
    data.enable_curriculum_learning=False \
    data.max_prompt_length="${max_prompt_length}" \
    data.max_response_length="${max_response_length}" \
    actor_rollout_ref.actor.use_dynamic_bsz=${use_dynamic_bsz} \
    actor_rollout_ref.ref.log_prob_use_dynamic_bsz=${use_dynamic_bsz} \
    actor_rollout_ref.rollout.log_prob_use_dynamic_bsz=${use_dynamic_bsz} \
    actor_rollout_ref.actor.ppo_max_token_len_per_gpu=${actor_ppo_max_token_len} \
    actor_rollout_ref.ref.log_prob_max_token_len_per_gpu=${infer_ppo_max_token_len} \
    actor_rollout_ref.rollout.log_prob_max_token_len_per_gpu=${infer_ppo_max_token_len} \
    actor_rollout_ref.actor.ulysses_sequence_parallel_size=${sp_size} \
    actor_rollout_ref.ref.ulysses_sequence_parallel_size=${sp_size} \
    actor_rollout_ref.model.path=$MODEL_PATH \
    actor_rollout_ref.actor.optim.lr=1e-6 \
    actor_rollout_ref.model.use_remove_padding=True \
    actor_rollout_ref.actor.ppo_mini_batch_size=32 \
    actor_rollout_ref.actor.use_kl_loss=True \
    actor_rollout_ref.actor.kl_loss_coef=0.001 \
    actor_rollout_ref.actor.kl_loss_type=low_var_kl \
    actor_rollout_ref.model.enable_gradient_checkpointing=True \
    actor_rollout_ref.actor.fsdp_config.param_offload=${offload} \
    actor_rollout_ref.actor.fsdp_config.optimizer_offload=${offload} \
    actor_rollout_ref.rollout.tensor_model_parallel_size=1 \
    actor_rollout_ref.rollout.name=vllm \
    actor_rollout_ref.rollout.gpu_memory_utilization=0.5 \
    actor_rollout_ref.rollout.n=16 \
    actor_rollout_ref.rollout.enforce_eager=True \
    actor_rollout_ref.rollout.free_cache_engine=False \
    actor_rollout_ref.rollout.enable_chunked_prefill=True \
    actor_rollout_ref.rollout.tensor_model_parallel_size=${gen_tp} \
    actor_rollout_ref.ref.fsdp_config.param_offload=${offload} \
    algorithm.kl_ctrl.kl_coef=0.001 \
    trainer.critic_warmup=0 \
    trainer.logger=['wandb'] \
    trainer.project_name='GRPO_combined_logic' \
    trainer.experiment_name='Qwen2.5-7B-Instruct-1M_combinedkk_nocl' \
    trainer.n_gpus_per_node=8 \
    trainer.nnodes=1 \
    trainer.default_local_dir=./local_save_dir/GRPO_Qwen2.5-7B-Instruct-1M_combinedkk_nocl \
    trainer.default_hdfs_dir=null \
    trainer.remove_previous_ckpt_in_save=True \
    trainer.hf_account=xxx \
    trainer.cleanup_after_upload=True \
    trainer.save_freq=50 \
    trainer.test_freq=10 \
    trainer.total_epochs=2 $@ 2>&1 | tee standardtraining_GRPO_Qwen2.5-7B-Instruct-1M_combinedkk_nocl.log
