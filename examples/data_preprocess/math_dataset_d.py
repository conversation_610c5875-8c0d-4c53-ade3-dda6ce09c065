"""
Preprocess pre-split MATH dataset from parquet format.
Includes functionality to cluster data based on text features and embeddings.
"""

import os
import pandas as pd
import numpy as np
import logging
import argparse

from verl.utils.hdfs_io import copy, makedirs
from verl.utils.embedding_clustering import assign_embedding_clusters
from verl.utils.reward_score.math import remove_boxed, last_boxed_only_string

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_solution(solution_str):
    """Extracts the final answer from the MATH dataset solution string."""
    # Handle potential None or non-string inputs gracefully
    if not isinstance(solution_str, str):
        logger.warning(f"Expected string, got {type(solution_str)}. Returning None.")
        return None

    return remove_boxed(last_boxed_only_string(solution_str))

# This function processes a single row (pandas Series)
def process_row(row, split_name, cluster_id=None):
    """Processes a single row of the DataFrame to the target format.

    Args:
        row: DataFrame row to process
        split_name: Name of the split (train, val, test)
        cluster_id: Optional cluster ID to include in the processed data
    """
    # Use renamed columns 'question' and 'answer'
    question_raw = row.get('question', None) # Use .get for safety
    answer_raw = row.get('answer', None)

    if question_raw is None or answer_raw is None:
        logger.error(f"Missing 'question' or 'answer' in row with index {row.name}. Skipping.")
        return None # Skip rows with missing essential data

    instruction_following = "Let's think step by step and output the final answer within \\boxed{}."
    question = question_raw + ' ' + instruction_following
    solution = extract_solution(answer_raw)

    if solution is None:
         logger.warning(f"Could not extract solution for row index {row.name}. Ground truth will be None.")
         # Decide how to handle this: skip row or keep with None solution?
         # Keeping with None for now, filter later if needed.

    data = {
        "data_source": "math",
        "difficulty_bin": "math_d_"+str(row.get('data_source', None)),
        "cluster": cluster_id,
        "prompt": [{
            "role": "user",
            "content": question,
        }],
        "ability": "math",
        "reward_model": {
            "style": "rule",
            "ground_truth": solution
        },
        "extra_info": {
            'split': split_name,
            'index': row.name, # Use pandas index as the unique identifier for the original row
            'answer': answer_raw, # Keep the original full answer string
            "question": question_raw, # Keep the original question string
        }
    }
    return data

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Preprocess pre-split MATH dataset parquet files.")
    parser.add_argument('--input_local_dir', default='~/data/math_d', help='Local directory containing the input parquet files (train.parquet, val.parquet, test.parquet)')
    parser.add_argument('--output_local_dir', default='~/data/math_d', help='Local directory to save the processed parquet files')
    parser.add_argument('--hdfs_dir', default=None, help='HDFS directory to copy the processed files to')

    # Clustering arguments
    parser.add_argument('--num_clusters', type=int, default=10, help='Number of clusters to create')
    parser.add_argument('--model_name', type=str, default=None, help='Name of the model to use for embeddings')
    parser.add_argument('--batch_size', type=int, default=32, help='Batch size for model inference')
    parser.add_argument('--text_features_weight', type=float, default=0.5, help='Weight for text features vs embeddings (0.5 means equal weight)')
    parser.add_argument('--visualize', action='store_true', help='Whether to create visualization plots')
    parser.add_argument('--embedding_dim', type=int, default=8, help='Target dimension for embedding reduction')
    parser.add_argument('--auto_adjust_clusters', action='store_true', help='Whether to automatically adjust number of clusters')
    parser.add_argument('--target_std_ratio', type=float, default=0.1, help='Target ratio of std/mean for cluster sizes (lower means more balanced)')
    parser.add_argument('--use_embedding', action='store_true', help='Whether to use embeddings in clustering')

    args = parser.parse_args()

    input_dir = os.path.expanduser(args.input_local_dir)
    output_dir = os.path.expanduser(args.output_local_dir)

    # Define input file paths
    train_input_path = os.path.join(input_dir, 'train_harder.parquet')
    val_input_path = os.path.join(input_dir, 'validation_harder.parquet')
    test_input_path = os.path.join(input_dir, 'test_harder.parquet')

    # Check if input files exist
    if not os.path.exists(train_input_path):
        logger.error(f"Input file not found: {train_input_path}")
        exit(1)
    if not os.path.exists(val_input_path):
        logger.error(f"Input file not found: {val_input_path}")
        exit(1)
    if not os.path.exists(test_input_path):
        logger.error(f"Input file not found: {test_input_path}")
        exit(1)

    # Load datasets
    logger.info(f"Loading datasets from {input_dir}")
    try:
        train_df = pd.read_parquet(train_input_path)
        val_df = pd.read_parquet(val_input_path)
        test_df = pd.read_parquet(test_input_path)
    except Exception as e:
        logger.error(f"Error loading parquet files: {e}")
        exit(1)

    logger.info(f"Loaded train dataset size: {len(train_df)}")
    logger.info(f"Loaded validation dataset size: {len(val_df)}")
    logger.info(f"Loaded test dataset size: {len(test_df)}")

    # Rename columns: 'problem' -> 'question', 'ground_truth' -> 'answer'
    logger.info("Renaming columns ('problem' -> 'question', 'ground_truth' -> 'answer')")
    column_mapping = {'problem': 'question', 'ground_truth': 'answer'}
    train_df.rename(columns=column_mapping, inplace=True)
    val_df.rename(columns=column_mapping, inplace=True)
    test_df.rename(columns=column_mapping, inplace=True)

    # Verify renaming (optional)
    logger.debug(f"Train columns after rename: {train_df.columns.tolist()}")
    logger.debug(f"Validation columns after rename: {val_df.columns.tolist()}")
    logger.debug(f"Test columns after rename: {test_df.columns.tolist()}")

    # Merge datasets for clustering
    logger.info("Merging datasets for clustering")
    merged_df = pd.concat([train_df, val_df, test_df], ignore_index=True)
    logger.info(f"Merged dataset size: {len(merged_df)}")

    # Create a column to track which split each row belongs to
    merged_df['split'] = 'unknown'
    merged_df.loc[:len(train_df)-1, 'split'] = 'train'
    merged_df.loc[len(train_df):len(train_df)+len(val_df)-1, 'split'] = 'val'
    merged_df.loc[len(train_df)+len(val_df):, 'split'] = 'test'

    # Apply clustering to the merged dataset
    logger.info("Applying clustering to the merged dataset")
    merged_df_with_clusters = assign_embedding_clusters(
        base_name="math_d",
        dataframe=merged_df,
        prompt_key='question',  # Use the question column for clustering
        num_clusters=args.num_clusters,
        model_name=args.model_name,
        bucket_column="cluster",  # Store directly in cluster column
        batch_size=args.batch_size,
        text_features_weight=args.text_features_weight,
        visualize=args.visualize,
        embedding_dim=args.embedding_dim,
        auto_adjust_clusters=args.auto_adjust_clusters,
        target_std_ratio=args.target_std_ratio,
        use_embedding=args.use_embedding
    )

    # Split back into train, val, test
    logger.info("Splitting clustered dataset back into train, val, test")
    train_df_with_clusters = merged_df_with_clusters[merged_df_with_clusters['split'] == 'train']
    val_df_with_clusters = merged_df_with_clusters[merged_df_with_clusters['split'] == 'val']
    test_df_with_clusters = merged_df_with_clusters[merged_df_with_clusters['split'] == 'test']

    # Process datasets with cluster information
    logger.info("Processing train dataset with cluster information")
    processed_train_list = train_df_with_clusters.apply(
        lambda row: process_row(row, 'train', row.get('cluster')), axis=1
    ).dropna().tolist()
    processed_train_df = pd.DataFrame(processed_train_list)

    logger.info("Processing validation dataset with cluster information")
    processed_val_list = val_df_with_clusters.apply(
        lambda row: process_row(row, 'val', row.get('cluster')), axis=1
    ).dropna().tolist()
    processed_val_df = pd.DataFrame(processed_val_list)

    logger.info("Processing test dataset with cluster information")
    processed_test_list = test_df_with_clusters.apply(
        lambda row: process_row(row, 'test', row.get('cluster')), axis=1
    ).dropna().tolist()
    processed_test_df = pd.DataFrame(processed_test_list)

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Define output file paths
    train_output_path = os.path.join(output_dir, 'train.parquet')
    val_output_path = os.path.join(output_dir, 'val.parquet')
    test_output_path = os.path.join(output_dir, 'test.parquet')

    # Save processed data to parquet files
    logger.info(f"Saving processed datasets to {output_dir}")
    logger.info(f"Processed train size: {len(processed_train_df)}")
    logger.info(f"Processed validation size: {len(processed_val_df)}")
    logger.info(f"Processed test size: {len(processed_test_df)}")

    try:
        processed_train_df.to_parquet(train_output_path, index=False)
        processed_val_df.to_parquet(val_output_path, index=False)
        processed_test_df.to_parquet(test_output_path, index=False)
    except Exception as e:
        logger.error(f"Error saving parquet files: {e}")
        exit(1)

    logger.info("Successfully saved processed data locally.")

    # Print cluster distribution
    logger.info("Cluster distribution in train set:")
    logger.info(processed_train_df['cluster'].value_counts())

    logger.info("Cluster distribution in validation set:")
    logger.info(processed_val_df['cluster'].value_counts())

    logger.info("Cluster distribution in test set:")
    logger.info(processed_test_df['cluster'].value_counts())

    print(processed_test_df.iloc[0])
    
    # Print distribution of clusters
    print(processed_train_df['cluster'].value_counts())
    print(processed_val_df['cluster'].value_counts())
    print(processed_test_df['cluster'].value_counts())

    logger.info("Script finished.")
