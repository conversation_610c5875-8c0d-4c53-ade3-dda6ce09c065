"""
Preprocess the MATH500 dataset to parquet format with clustering
"""

import os
import datasets
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
import logging

from verl.utils.hdfs_io import copy, makedirs
from verl.utils.embedding_clustering import assign_embedding_clusters
import argparse

from verl.utils.reward_score.math import remove_boxed, last_boxed_only_string

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def extract_solution(solution_str):
    return solution_str

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--local_dir', default='~/ne-data/math500')
    parser.add_argument('--hdfs_dir', default=None)
    parser.add_argument('--num_clusters', type=int, default=10, help='Number of clusters for embedding clustering')
    parser.add_argument('--val_size', type=float, default=0.2, help='Validation set size as a fraction of training data')
    parser.add_argument('--random_seed', type=int, default=1, help='Random seed for reproducibility')
    parser.add_argument('--length_weight', type=float, default=1, help='Weight for length feature in clustering')

    args = parser.parse_args()

    # 'lighteval/MATH' is no longer available on huggingface.
    # Use mirror repo: DigitalLearningGmbH/MATH-lighteval
    data_source = 'HuggingFaceH4/MATH-500'
    logger.info(f"Loading dataset from {data_source}")
    dataset = datasets.load_dataset(data_source, trust_remote_code=True)

    original_dataset = dataset['test']  # 只使用train集
    logger.info(f"Original dataset size: {len(original_dataset)}")

    instruction_following = "Let's think step by step and output the final answer within \\boxed{}."

    def make_map_fn(split):
        def process_fn(example, idx):
            question = example.pop('problem')
            question = question + ' ' + instruction_following
            answer = example.pop('answer')
            solution = extract_solution(answer)
            data = {
                "data_source": data_source,
                "prompt": [{
                    "role": "user",
                    "content": question
                }],
                "ability": "math",
                "reward_model": {
                    "style": "rule",
                    "ground_truth": solution
                },
                "extra_info": {
                    'split': split,
                    'index': idx
                }
            }
            return data
        return process_fn

    # Process dataset
    logger.info("Processing dataset")
    processed_dataset = original_dataset.map(function=make_map_fn('train'), with_indices=True)

    # Convert to pandas DataFrame
    df = processed_dataset.to_pandas()

    # First split into train+val and test
    train_val_size = 0.8  # 80% for train+val
    train_val_df, test_df = train_test_split(
        df,
        train_size=train_val_size,
        random_state=args.random_seed
    )

    # Then split train_val into train and validation
    train_size = 0.875  # 70% of total (0.875 * 0.8 = 0.7)
    train_df, val_df = train_test_split(
        train_val_df,
        train_size=train_size,
        random_state=args.random_seed
    )

    # Apply embedding clustering to all data
    logger.info("Combining datasets for clustering")
    combined_df = pd.concat([train_df, val_df, test_df], ignore_index=True)

    # Apply embedding clustering
    logger.info(f"Applying embedding clustering with {args.num_clusters} clusters")
    clustered_df = assign_embedding_clusters(
        base_name='math',
        dataframe=combined_df,
        prompt_key='prompt',
        num_clusters=args.num_clusters,
        model_name=None,
        bucket_column='cluster',
        batch_size=32,
        text_features_weight=args.length_weight,
        visualize=False,
        use_embedding=False
    )

    # Split back into train, val and test using the original indices
    final_train_df = clustered_df.iloc[:len(train_df)].reset_index(drop=True)
    final_val_df = clustered_df.iloc[len(train_df):len(train_df)+len(val_df)].reset_index(drop=True)
    final_test_df = clustered_df.iloc[len(train_df)+len(val_df):].reset_index(drop=True)

    # Create output directory
    local_dir = os.path.expanduser(args.local_dir)
    os.makedirs(local_dir, exist_ok=True)

    # Save to parquet files
    logger.info(f"Saving datasets to {local_dir}")
    logger.info(f"Final train size: {len(final_train_df)} ({len(final_train_df)/len(clustered_df):.2%})")
    logger.info(f"Validation size: {len(final_val_df)} ({len(final_val_df)/len(clustered_df):.2%})")
    logger.info(f"Test size: {len(final_test_df)} ({len(final_test_df)/len(clustered_df):.2%})")

    final_train_df.to_parquet(os.path.join(local_dir, 'train.parquet'))
    final_val_df.to_parquet(os.path.join(local_dir, 'val.parquet'))
    final_test_df.to_parquet(os.path.join(local_dir, 'test.parquet'))

    # Log cluster distribution
    cluster_distribution = final_train_df['data_source'].value_counts().to_dict()
    logger.info(f"Train cluster distribution: {cluster_distribution}")

    val_cluster_distribution = final_val_df['data_source'].value_counts().to_dict()
    logger.info(f"Validation cluster distribution: {val_cluster_distribution}")

    test_cluster_distribution = final_test_df['data_source'].value_counts().to_dict()
    logger.info(f"Test cluster distribution: {test_cluster_distribution}")

    # Copy to HDFS if specified
    if args.hdfs_dir is not None:
        makedirs(args.hdfs_dir)
        copy(src=local_dir, dst=args.hdfs_dir)
