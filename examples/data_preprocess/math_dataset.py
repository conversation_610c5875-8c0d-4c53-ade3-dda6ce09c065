"""
Preprocess the MATH-lighteval dataset to parquet format with embedding clustering
"""

import os
import datasets
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
import logging

from verl.utils.hdfs_io import copy, makedirs
from verl.utils.embedding_clustering import assign_embedding_clusters
import argparse

from verl.utils.reward_score.math import remove_boxed, last_boxed_only_string

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_solution(solution_str):
    return remove_boxed(last_boxed_only_string(solution_str))

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--local_dir', default='~/ne-data/math')
    parser.add_argument('--hdfs_dir', default=None)
    parser.add_argument('--num_clusters', type=int, default=10, help='Number of clusters for embedding clustering')
    parser.add_argument('--val_size', type=float, default=0.2, help='Validation set size as a fraction of training data')
    parser.add_argument('--random_seed', type=int, default=42, help='Random seed for reproducibility')
    parser.add_argument('--length_weight', type=float, default=1, help='Weight for length feature in clustering')

    args = parser.parse_args()

    # 'lighteval/MATH' is no longer available on huggingface.
    # Use mirror repo: DigitalLearningGmbH/MATH-lighteval
    data_source = 'DigitalLearningGmbH/MATH-lighteval'
    logger.info(f"Loading dataset from {data_source}")
    dataset = datasets.load_dataset(data_source, trust_remote_code=True)

    original_train_dataset = dataset['train']
    test_dataset = dataset['test']

    logger.info(f"Original train dataset size: {len(original_train_dataset)}")
    logger.info(f"Test dataset size: {len(test_dataset)}")

    instruction_following = "Let's think step by step and output the final answer within \\boxed{}."

    def make_map_fn(split):
        def process_fn(example, idx):
            question = example.pop('problem')
            question = question + ' ' + instruction_following
            answer = example.pop('solution')
            solution = extract_solution(answer)
            data = {
                "data_source": data_source,
                "prompt": [{
                    "role": "user",
                    "content": question
                }],
                "ability": "math",
                "reward_model": {
                    "style": "rule",
                    "ground_truth": solution
                },
                "extra_info": {
                    'split': split,
                    'index': idx
                }
            }
            return data
        return process_fn

    # Process both datasets with the same function
    logger.info("Processing train dataset")
    processed_train = original_train_dataset.map(function=make_map_fn('train'), with_indices=True)

    logger.info("Processing test dataset")
    processed_test = test_dataset.map(function=make_map_fn('test'), with_indices=True)

    # Convert to pandas DataFrames for easier manipulation
    train_df = processed_train.to_pandas()
    test_df = processed_test.to_pandas()

    # Combine datasets for clustering
    logger.info("Combining datasets for clustering")
    combined_df = pd.concat([train_df, test_df], ignore_index=True)

    # Apply embedding clustering to the combined dataset
    logger.info(f"Applying embedding clustering with {args.num_clusters} clusters")
    clustered_df = assign_embedding_clusters(
        base_name='math',
        dataframe=combined_df,
        prompt_key='prompt',
        num_clusters=args.num_clusters,
        model_name=None,
        bucket_column='cluster',
        batch_size=32,
        text_features_weight=args.length_weight,
        visualize=False,
        use_embedding=False
    )

    # Split back into train and test
    train_size = len(train_df)
    clustered_train_df = clustered_df.iloc[:train_size]
    clustered_test_df = clustered_df.iloc[train_size:]

    # Split train into train and validation
    logger.info(f"Splitting train dataset into train and validation (val_size={args.val_size})")
    train_indices, val_indices = train_test_split(
        np.arange(len(clustered_train_df)),
        test_size=args.val_size,
        random_state=args.random_seed
    )

    final_train_df = clustered_train_df.iloc[train_indices].reset_index(drop=True)
    val_df = clustered_train_df.iloc[val_indices].reset_index(drop=True)

    # Update split information in extra_info
    for i in range(len(val_df)):
        val_df.at[i, 'extra_info']['split'] = 'val'

    # Create output directory
    local_dir = os.path.expanduser(args.local_dir)
    os.makedirs(local_dir, exist_ok=True)

    # Save to parquet files
    logger.info(f"Saving datasets to {local_dir}")
    logger.info(f"Final train size: {len(final_train_df)}")
    logger.info(f"Validation size: {len(val_df)}")
    logger.info(f"Test size: {len(clustered_test_df)}")

    final_train_df.to_parquet(os.path.join(local_dir, 'train.parquet'))
    val_df.to_parquet(os.path.join(local_dir, 'val.parquet'))
    clustered_test_df.to_parquet(os.path.join(local_dir, 'test.parquet'))

    # Log cluster distribution
    cluster_distribution = final_train_df['data_source'].value_counts().to_dict()
    logger.info(f"Train cluster distribution: {cluster_distribution}")

    val_cluster_distribution = val_df['data_source'].value_counts().to_dict()
    logger.info(f"Validation cluster distribution: {val_cluster_distribution}")

    test_cluster_distribution = clustered_test_df['data_source'].value_counts().to_dict()
    logger.info(f"Test cluster distribution: {test_cluster_distribution}")

    # Copy to HDFS if specified
    if args.hdfs_dir is not None:
        makedirs(args.hdfs_dir)
        copy(src=local_dir, dst=args.hdfs_dir)
