import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
import seaborn as sns
import os

# --- 配置参数 ---
FILE_PATH = 'Qwen2.5-Math-7B--GSM8K--difficulty.csv' # CSV 文件路径
OUTPUT_DIR = '.'                    # 输出目录
SPLIT_RATIO = (0.7, 0.2, 0.1)                     # 训练:验证:测试 比例
N_BINS = 10                                       # 难度分箱数量，用于分层抽样
RANDOM_STATE = 42                                 # 随机种子，保证结果可复现
OUTPUT_BIN_COL_NAME = 'data_source'               # 输出的分箱列名
DIFFICULTY_COL = 'solved_percentage'             # 难度列名

# --- 难度偏好参数 ---
PREFERENCE_SETTINGS = {
    'harder': {'quantile': 0.6, 'mode': 'below'},
    'normal': {'quantile': None, 'mode': 'all'},
    'easier': {'quantile': 0.4, 'mode': 'above'}
}

def create_output_dir(dir_name):
    """创建输出目录"""
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
        print(f"Created output directory: {dir_name}")

def plot_difficulty_distribution(df_dict, difficulty_col, title_suffix):
    """绘制难度分布图"""
    plt.figure(figsize=(12, 6))
    for name, df in df_dict.items():
        sns.histplot(df[difficulty_col], kde=True, label=f'{name} (n={len(df)})', stat="density", common_norm=False, bins=30, alpha=0.6)

    plt.title(f'Difficulty Distribution ({difficulty_col}) - {title_suffix}')
    plt.xlabel('Solved Percentage (Lower is harder)')
    plt.ylabel('Density')
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plot_filename = os.path.join(OUTPUT_DIR, f'difficulty_distribution_{title_suffix.lower().replace(" ", "_")}.png')
    plt.savefig(plot_filename)
    print(f"Saved distribution plot: {plot_filename}")
    plt.show()


def split_dataset_by_difficulty(
    file_path: str,
    difficulty_col: str,
    ratio: tuple = (0.7, 0.2, 0.1),
    preference: str = 'harder',
    n_bins: int = 10,
    random_state: int = 42,
    output_bin_col_name: str = 'data_source'
):
    """
    根据难度筛选和划分数据集, 并添加难度等级列.

    Args:
        file_path (str): CSV文件路径.
        difficulty_col (str): 包含难度指标的列名.
        ratio (tuple): (train, validation, test) 比例.
        preference (str): 难度偏好 ('harder', 'normal', 'easier').
        n_bins (int): 用于分层抽样的难度分箱数.
        random_state (int): 随机种子.
        output_bin_col_name (str): 输出数据中难度等级列的名称.

    Returns:
        tuple: (train_df, val_df, test_df) DataFrames, 包含难度等级列.
    """
    print(f"\n--- Starting data splitting with preference: '{preference}' ---")

    # 1. 加载数据
    try:
        df = pd.read_csv(file_path)
        print(f"Loaded data: {df.shape[0]} rows, {df.shape[1]} columns")
    except FileNotFoundError:
        print(f"Error: File not found at {file_path}")
        return None, None, None
    except Exception as e:
        print(f"Error loading CSV file: {e}")
        return None, None, None

    if difficulty_col not in df.columns:
        print(f"Error: Difficulty column '{difficulty_col}' not found in the CSV.")
        return None, None, None

    initial_rows = len(df)
    df = df.dropna(subset=[difficulty_col])
    if len(df) < initial_rows:
        print(f"Removed {initial_rows - len(df)} rows with missing values in '{difficulty_col}'.")

    if df.empty:
        print("Error: No data remaining after handling missing values.")
        return None, None, None

    # 2. 难度偏好筛选
    pref_setting = PREFERENCE_SETTINGS.get(preference)
    if not pref_setting:
        print(f"Warning: Invalid preference '{preference}'. Using 'normal'.")
        preference = 'normal'
        pref_setting = PREFERENCE_SETTINGS['normal']

    original_count = len(df)
    if pref_setting['mode'] != 'all':
        quantile_val = df[difficulty_col].quantile(pref_setting['quantile'])
        if pref_setting['mode'] == 'below':
            df = df[df[difficulty_col] <= quantile_val]
            print(f"Applied '{preference}' preference: Kept data with {difficulty_col} <= {quantile_val:.4f} ({pref_setting['quantile'] * 100}th percentile).")
        elif pref_setting['mode'] == 'above':
            df = df[df[difficulty_col] >= quantile_val]
            print(f"Applied '{preference}' preference: Kept data with {difficulty_col} >= {quantile_val:.4f} ({pref_setting['quantile'] * 100}th percentile).")
        print(f"Filtered data size: {len(df)} rows (from {original_count})")
    else:
        print("Using 'normal' preference: Keeping all data.")

    if len(df) < 10:
        print("Error: Not enough data remaining after preference filtering to perform splitting.")
        return None, None, None

    # 3. 创建难度分箱用于分层，并创建难度等级列
    temp_bin_col = '_temp_difficulty_bin' # 临时的分箱列名
    try:
        df[temp_bin_col] = pd.qcut(df[difficulty_col], q=n_bins, labels=False, duplicates='drop')
        num_unique_bins = df[temp_bin_col].nunique()
        print(f"Created {num_unique_bins} temporary difficulty bins for stratification using qcut.")
    except ValueError as e:
        print(f"Warning: Could not create {n_bins} bins using qcut due to data distribution. Trying pd.cut. Error: {e}")
        try:
             df[temp_bin_col] = pd.cut(df[difficulty_col], bins=n_bins, labels=False, include_lowest=True, duplicates='drop')
             num_unique_bins = df[temp_bin_col].nunique()
             print(f"Used pd.cut instead, created {num_unique_bins} temporary bins.")
        except Exception as cut_e:
             print(f"Error creating bins even with pd.cut: {cut_e}. Cannot proceed with stratification.")
             return None, None, None

    # 创建难度等级列 (编号越大越难)
    # rank = k - bin_number (maps bin 0 -> k, bin k-1 -> 1)
    # Note: .astype(int) is important if temp_bin_col contains NaNs after cut/qcut with errors/drops
    df[output_bin_col_name] = (num_unique_bins - df[temp_bin_col].astype('Int64')).astype('Int64') # 使用Int64支持可能的NA
    print(f"Created difficulty rank column '{output_bin_col_name}' (1 to {num_unique_bins}, higher is harder).")

    # 4. 分层抽样 (Stratify using the *original* temporary bins)
    train_ratio, val_ratio, test_ratio = ratio
    temp_train_ratio = train_ratio + val_ratio
    final_train_ratio_in_temp = train_ratio / temp_train_ratio

    stratify_col = df[temp_bin_col] if df[temp_bin_col].isnull().sum() == 0 else None # 仅在无缺失值时分层
    if stratify_col is None:
         print(f"Warning: Found NaN values in temporary bin column '{temp_bin_col}'. Splitting without stratification.")


    # 第一次分割: (train+val) vs test
    try:
        temp_train_val_df, test_df = train_test_split(
            df,
            test_size=test_ratio,
            stratify=stratify_col, # Use original bins for stratification
            random_state=random_state
        )
    except ValueError as e:
        print(f"Error during first split (test set): {e}. Trying without stratification.")
        temp_train_val_df, test_df = train_test_split(
            df, test_size=test_ratio, random_state=random_state
        )

    # 更新第二次分割的分层依据
    stratify_col_temp = temp_train_val_df[temp_bin_col] if temp_bin_col in temp_train_val_df and temp_train_val_df[temp_bin_col].isnull().sum() == 0 else None
    if stratify_col is not None and stratify_col_temp is None: # Check if stratification failed only for the second step
        print(f"Warning: Found NaN values in temporary bin column '{temp_bin_col}' within the temp_train_val set. Splitting train/val without stratification.")


    # 第二次分割: train vs val
    try:
        train_df, val_df = train_test_split(
            temp_train_val_df,
            train_size=final_train_ratio_in_temp,
            stratify=stratify_col_temp, # Use original bins for stratification
            random_state=random_state
        )
    except ValueError as e:
        print(f"Error during second split (train/val): {e}. Trying without stratification.")
        train_df, val_df = train_test_split(
            temp_train_val_df, train_size=final_train_ratio_in_temp, random_state=random_state
        )

    # 移除临时的分箱列 (保留 output_bin_col_name)
    train_df = train_df.drop(columns=[temp_bin_col], errors='ignore')
    val_df = val_df.drop(columns=[temp_bin_col], errors='ignore')
    test_df = test_df.drop(columns=[temp_bin_col], errors='ignore')

    print("\n--- Dataset Split Summary ---")
    print(f"Total data used for splitting ({preference} preference): {len(df)}")
    print(f"Train set size: {len(train_df)} ({len(train_df)/len(df):.2%})")
    print(f"Validation set size: {len(val_df)} ({len(val_df)/len(df):.2%})")
    print(f"Test set size: {len(test_df)} ({len(test_df)/len(df):.2%})")
    print(f"Output datasets include difficulty rank column: '{output_bin_col_name}'")


    # 5. 可视化难度分布
    datasets = {'Train': train_df, 'Validation': val_df, 'Test': test_df}
    plot_difficulty_distribution(datasets, difficulty_col, f"Preference_{preference.capitalize()}")

    # 6. 保存为 Parquet
    try:
        create_output_dir(OUTPUT_DIR)
        train_path = os.path.join(OUTPUT_DIR, f'train_{preference}.parquet')
        val_path = os.path.join(OUTPUT_DIR, f'validation_{preference}.parquet')
        test_path = os.path.join(OUTPUT_DIR, f'test_{preference}.parquet')

        train_df.to_parquet(train_path, index=False)
        val_df.to_parquet(val_path, index=False)
        test_df.to_parquet(test_path, index=False)
        print(f"Saved datasets to Parquet format in '{OUTPUT_DIR}' directory:")
        print(f" - {train_path}")
        print(f" - {val_path}")
        print(f" - {test_path}")
    except Exception as e:
        print(f"Error saving datasets to Parquet: {e}")

    print(f"--- Finished splitting for preference: '{preference}' ---")
    return train_df, val_df, test_df

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser(description="Split dataset by difficulty and preference.")
    parser.add_argument('--file_path', default=FILE_PATH, help='Path to the input CSV file')
    parser.add_argument('--output_dir', default=OUTPUT_DIR, help='Directory to save the output Parquet files')
    parser.add_argument('--split_ratio', default=SPLIT_RATIO, type=tuple, help='Train, validation, test split ratio')
    parser.add_argument('--n_bins', default=N_BINS, type=int, help='Number of bins for stratification')
    parser.add_argument('--random_state', default=RANDOM_STATE, type=int, help='Random state for reproducibility')
    parser.add_argument('--output_bin_col_name', default=OUTPUT_BIN_COL_NAME, help='Name of the output bin column')
    parser.add_argument('--difficulty_col', default=DIFFICULTY_COL, help='Name of the difficulty column in the CSV')
    args = parser.parse_args()
    preferences_to_run = ['harder', 'normal', 'easier']
    results = {}
    for pref in preferences_to_run:
        train, val, test = split_dataset_by_difficulty(
            file_path=args.file_path,
            difficulty_col=args.difficulty_col,
            ratio=args.split_ratio,
            preference=pref,
            n_bins=args.n_bins,
            random_state=args.random_state,
            output_bin_col_name=args.output_bin_col_name # Pass the desired output column name
        )
        if train is not None:
            results[pref] = {'train': train, 'validation': val, 'test': test}
    # 检查 'normal' 偏好下训练集的 'data_source' 列
    if 'normal' in results:
        print("\n--- Sample of 'normal' preference train set (with data_source column) ---")
        print(results['normal']['train'].head())
        if OUTPUT_BIN_COL_NAME in results['normal']['train'].columns:
            print(f"\nValue counts for '{OUTPUT_BIN_COL_NAME}' in 'normal' train set:")
            print(results['normal']['train'][OUTPUT_BIN_COL_NAME].value_counts().sort_index())