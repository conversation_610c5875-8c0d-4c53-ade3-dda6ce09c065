
"""
Preprocess the KK dataset to parquet format with embedding clustering
"""

import os
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
import logging

from verl.utils.hdfs_io import copy, makedirs
from verl.utils.embedding_clustering import assign_embedding_clusters
import argparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--input_dir', default='/data/yzr/DUMP/combined_logic_dataset/generate_combined_kk')
    parser.add_argument('--local_dir', default='~/pdata/kk')
    parser.add_argument('--hdfs_dir', default=None)
    parser.add_argument('--num_clusters', type=int, default=10, help='Number of clusters for embedding clustering')
    parser.add_argument('--val_size', type=float, default=0.2, help='Validation set size as a fraction of training data')
    parser.add_argument('--random_seed', type=int, default=42, help='Random seed for reproducibility')
    parser.add_argument('--length_weight', type=float, default=0.5, help='Weight for length feature in clustering')

    args = parser.parse_args()

    # Load the datasets
    logger.info("Loading datasets from parquet files")
    train_df = pd.read_parquet(os.path.join(args.input_dir, 'combined_logic_datasets_train.parquet'))
    test_df = pd.read_parquet(os.path.join(args.input_dir, 'combined_logic_datasets_test.parquet'))

    logger.info(f"Original train dataset size: {len(train_df)}")
    logger.info(f"Test dataset size: {len(test_df)}")

    # Combine datasets for clustering
    logger.info("Combining datasets for clustering")
    combined_df = pd.concat([train_df, test_df], ignore_index=True)

    # Apply embedding clustering to the combined dataset
    logger.info(f"Applying embedding clustering with {args.num_clusters} clusters")
    clustered_df = assign_embedding_clusters(
        base_name='kk',
        dataframe=combined_df,
        prompt_key='prompt',
        num_clusters=args.num_clusters,
        model_name=None,
        bucket_column='data_source',
        batch_size=32,
        text_features_weight=args.length_weight,
        visualize=True
    )

    # Split back into train and test
    train_size = len(train_df)
    clustered_train_df = clustered_df.iloc[:train_size]
    clustered_test_df = clustered_df.iloc[train_size:]

    # Split train into train and validation
    logger.info(f"Splitting train dataset into train and validation (val_size={args.val_size})")
    train_indices, val_indices = train_test_split(
        np.arange(len(clustered_train_df)),
        test_size=args.val_size,
        random_state=args.random_seed
    )

    final_train_df = clustered_train_df.iloc[train_indices].reset_index(drop=True)
    val_df = clustered_train_df.iloc[val_indices].reset_index(drop=True)

    # Update split information in extra_info
    for i in range(len(val_df)):
        if isinstance(val_df.at[i, 'extra_info'], dict):
            val_df.at[i, 'extra_info']['split'] = 'val'

    # Create output directory
    local_dir = os.path.expanduser(args.local_dir)
    os.makedirs(local_dir, exist_ok=True)

    # Save to parquet files
    logger.info(f"Saving datasets to {local_dir}")
    logger.info(f"Final train size: {len(final_train_df)}")
    logger.info(f"Validation size: {len(val_df)}")
    logger.info(f"Test size: {len(clustered_test_df)}")

    final_train_df.to_parquet(os.path.join(local_dir, 'train.parquet'))
    val_df.to_parquet(os.path.join(local_dir, 'val.parquet'))
    clustered_test_df.to_parquet(os.path.join(local_dir, 'test.parquet'))

    # Log cluster distribution
    cluster_distribution = final_train_df['data_source'].value_counts().to_dict()
    logger.info(f"Train cluster distribution: {cluster_distribution}")

    val_cluster_distribution = val_df['data_source'].value_counts().to_dict()
    logger.info(f"Validation cluster distribution: {val_cluster_distribution}")

    test_cluster_distribution = clustered_test_df['data_source'].value_counts().to_dict()
    logger.info(f"Test cluster distribution: {test_cluster_distribution}")

    # Copy to HDFS if specified
    if args.hdfs_dir is not None:
        logger.info(f"Copying data to HDFS: {args.hdfs_dir}")
        makedirs(args.hdfs_dir)
        copy(src=local_dir, dst=args.hdfs_dir)

