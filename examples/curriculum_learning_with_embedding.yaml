# Example configuration for curriculum learning with embedding-based clustering
# This configuration shows how to enable automatic data bucketing by embedding and clustering
# for curriculum learning in PPO training.

data:
  # Enable curriculum learning
  enable_curriculum_learning: true

  # Don't specify data_source_key to use automatic bucketing
  # data_source_key: null

  # Enable embedding-based clustering
  auto_bucket_by_embedding: true

  # Number of clusters to create
  num_embedding_clusters: 5

  # Method for embedding extraction
  # Options: 'tfidf' (default), 'sentence-transformers'
  embedding_method: 'sentence-transformers'

  # Method for clustering
  # Options: 'kmeans' (default)
  clustering_method: 'kmeans'

  # Model name for embeddings (only used if embedding_method='sentence-transformers')
  embedding_model_name: 'sentence-transformers/all-MiniLM-L6-v2'

  # Other standard data configuration
  train_files: 'path/to/train.parquet'
  val_files: 'path/to/val.parquet'
  prompt_key: 'prompt'
  max_prompt_length: 1024
  train_batch_size: 32
  shuffle: true

# Other configuration sections (trainer, model, etc.) would go here
