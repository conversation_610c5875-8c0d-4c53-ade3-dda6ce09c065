#!/usr/bin/env python3
"""
Example script demonstrating influence function-based curriculum learning.

This script shows how to:
1. Set up influence function calculation
2. Integrate with curriculum learning
3. Monitor influence scores and sampling weights
4. Compare with perplexity-based methods
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List
import logging

# Import influence function modules
from verl.utils.influence_functions import create_influence_calculator
from verl.utils.curriculum_learning import LearnabilityEstimator, CurriculumController, CurriculumSampler

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleLanguageModel(nn.Module):
    """Simple language model for demonstration."""
    
    def __init__(self, vocab_size=1000, embed_dim=128, hidden_dim=256):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, embed_dim)
        self.transformer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(embed_dim, nhead=8, dim_feedforward=hidden_dim),
            num_layers=2
        )
        self.output_proj = nn.Linear(embed_dim, vocab_size)
        
    def forward(self, input_ids, attention_mask=None):
        # Simple forward pass
        x = self.embedding(input_ids)
        x = self.transformer(x.transpose(0, 1)).transpose(0, 1)
        logits = self.output_proj(x)
        return type('ModelOutput', (), {'logits': logits})()


def create_sample_data(num_samples=100, seq_len=32, vocab_size=1000):
    """Create sample training data with different data sources."""
    data = []
    sources = ['math', 'code', 'text', 'reasoning']
    
    for i in range(num_samples):
        source = sources[i % len(sources)]
        
        # Create synthetic data with different difficulty levels per source
        if source == 'math':
            # Math problems tend to be harder
            input_ids = torch.randint(0, vocab_size, (seq_len,))
            difficulty_multiplier = 1.5
        elif source == 'code':
            # Code problems have medium difficulty
            input_ids = torch.randint(0, vocab_size, (seq_len,))
            difficulty_multiplier = 1.2
        elif source == 'text':
            # Text is easier
            input_ids = torch.randint(0, vocab_size, (seq_len,))
            difficulty_multiplier = 0.8
        else:  # reasoning
            # Reasoning is hardest
            input_ids = torch.randint(0, vocab_size, (seq_len,))
            difficulty_multiplier = 1.8
        
        data.append({
            'input_ids': input_ids,
            'attention_mask': torch.ones(seq_len),
            'data_source': source,
            'difficulty_multiplier': difficulty_multiplier
        })
    
    return data


def simple_loss_function(model_output, batch):
    """Simple cross-entropy loss function."""
    logits = model_output.logits
    # Create dummy labels (in practice, these would be real labels)
    labels = torch.randint(0, logits.size(-1), (logits.size(0), logits.size(1)))
    
    return nn.functional.cross_entropy(
        logits.view(-1, logits.size(-1)),
        labels.view(-1),
        ignore_index=-100
    )


def demonstrate_influence_functions():
    """Demonstrate influence function-based curriculum learning."""
    
    print("=== Influence Function-Based Curriculum Learning Demo ===\n")
    
    # 1. Create model and data
    print("1. Setting up model and data...")
    model = SimpleLanguageModel()
    train_data = create_sample_data(num_samples=80)
    val_data = create_sample_data(num_samples=20)
    
    print(f"   Created model with {sum(p.numel() for p in model.parameters())} parameters")
    print(f"   Training data: {len(train_data)} samples")
    print(f"   Validation data: {len(val_data)} samples")
    
    # 2. Set up influence function calculator
    print("\n2. Setting up influence function calculator...")
    influence_config = {
        'use_kfac': True,
        'regularization_lambda': 1e-3,
        'damping_factor': 1e-3,
        'max_samples_per_batch': 8,
        'kfac_damping': 1e-3
    }
    
    influence_calculator = create_influence_calculator(model, influence_config)
    print(f"   Created {'K-FAC' if influence_config['use_kfac'] else 'standard'} influence calculator")
    
    # 3. Set up curriculum learning
    print("\n3. Setting up curriculum learning...")
    
    # Create mock dataset for curriculum sampler
    class MockDataset:
        def __init__(self, data):
            self.data = data
        
        def __len__(self):
            return len(self.data)
        
        def __getitem__(self, idx):
            return self.data[idx]
    
    mock_dataset = MockDataset(train_data)
    curriculum_sampler = CurriculumSampler(
        dataset=mock_dataset,
        data_source_key='data_source',
        batch_size=8
    )
    
    # Enable influence functions
    curriculum_sampler.enable_influence_functions(True)
    print("   Enabled influence function-based curriculum learning")
    
    # 4. Simulate training with influence function updates
    print("\n4. Simulating training with influence function updates...")
    
    for step in range(5):
        print(f"\n   Step {step + 1}:")
        
        # Sample a batch
        batch_indices = np.random.choice(len(train_data), size=8, replace=False)
        batch_data = [train_data[i] for i in batch_indices]
        
        # Compute influence scores for each sample
        influence_scores = []
        for sample in batch_data:
            try:
                # Create single sample batch
                sample_batch = {
                    'input_ids': sample['input_ids'].unsqueeze(0),
                    'attention_mask': sample['attention_mask'].unsqueeze(0)
                }
                
                # Create validation batch
                val_batch = {
                    'input_ids': torch.stack([v['input_ids'] for v in val_data[:4]]),
                    'attention_mask': torch.stack([v['attention_mask'] for v in val_data[:4]])
                }
                
                # Compute influence score
                influence_score = influence_calculator.compute_influence_score(
                    sample_batch, val_batch, simple_loss_function
                )
                influence_scores.append(influence_score)
                
            except Exception as e:
                print(f"     Warning: Failed to compute influence for sample: {e}")
                influence_scores.append(0.0)
        
        # Organize data by source
        source_rewards = {}
        source_advantages = {}
        source_influence_scores = {}
        
        for i, sample in enumerate(batch_data):
            source = sample['data_source']
            
            if source not in source_rewards:
                source_rewards[source] = []
                source_advantages[source] = []
                source_influence_scores[source] = []
            
            # Simulate rewards and advantages based on difficulty
            difficulty = sample['difficulty_multiplier']
            reward = np.random.normal(2.0 / difficulty, 0.5)  # Harder tasks get lower rewards
            advantage = np.random.normal(1.0 / difficulty, 0.3)
            
            source_rewards[source].append(reward)
            source_advantages[source].append(advantage)
            source_influence_scores[source].append(influence_scores[i])
        
        # Update curriculum weights
        curriculum_sampler.update_weights(
            source_rewards=source_rewards,
            source_advantages=source_advantages,
            source_influence_scores=source_influence_scores
        )
        
        # Display results
        stats = curriculum_sampler.get_source_stats()
        print("     Source statistics:")
        for source, stat in stats.items():
            if source not in ['weight_trend', 'source_selection_history']:
                print(f"       {source}:")
                print(f"         Expected value: {stat['expected_value']:.4f}")
                print(f"         Sampling weight: {stat.get('sampling_weight', 0.0):.4f}")
                print(f"         Mean influence score: {stat.get('mean_influence_score', 0.0):.4f}")
                print(f"         Using influence functions: {stat.get('use_influence_functions', False)}")
    
    # 5. Compare with baseline method (original reward-advantage formula)
    print("\n5. Comparing with baseline method...")

    # Create a second curriculum sampler without influence functions
    curriculum_sampler_baseline = CurriculumSampler(
        dataset=mock_dataset,
        data_source_key='data_source',
        batch_size=8
    )
    # Note: baseline sampler uses influence_functions=False by default

    # Update baseline with same reward/advantage data (no influence scores)
    curriculum_sampler_baseline.update_weights(
        source_rewards=source_rewards,
        source_advantages=source_advantages
    )

    print("   Comparison of final sampling weights:")
    stats_influence = curriculum_sampler.get_source_stats()
    stats_baseline = curriculum_sampler_baseline.get_source_stats()

    print("   Source          | Influence-based | Baseline (Reward-Adv)")
    print("   ----------------|-----------------|----------------------")
    for source in ['math', 'code', 'text', 'reasoning']:
        if source in stats_influence and source in stats_baseline:
            weight_inf = stats_influence[source].get('sampling_weight', 0.0)
            weight_base = stats_baseline[source].get('sampling_weight', 0.0)
            print(f"   {source:<15} | {weight_inf:>13.4f} | {weight_base:>20.4f}")
    
    print("\n=== Demo Complete ===")
    print("\nKey takeaways:")
    print("- Influence functions provide a principled way to measure sample importance")
    print("- K-FAC approximation makes Hessian computation tractable")
    print("- Integration with curriculum learning enables adaptive sample selection")
    print("- The system gracefully falls back to original reward-advantage formula if needed")
    print("- Perplexity-based scoring has been removed in favor of the simpler, more direct approach")


if __name__ == "__main__":
    # Set random seeds for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        demonstrate_influence_functions()
    except Exception as e:
        print(f"Error running demo: {e}")
        print("This is expected if running without proper model setup.")
        print("The demo shows the intended usage pattern.")
