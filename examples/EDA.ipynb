{"cells": [{"cell_type": "code", "execution_count": 6, "id": "12f6fe90", "metadata": {}, "outputs": [], "source": ["# FILE_FOLDER = \"~/data/gsm8k_d/train_harder.parquet\"\n", "FILE_FOLDER = \"~/ne-data/math/train.parquet\"\n", "\n", "import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 7, "id": "41a12a0f", "metadata": {}, "outputs": [{"data": {"text/plain": ["<bound method NDFrame.head of         level                  type                         data_source  \\\n", "0     Level 5               Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "1     Level 5  Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "2     Level 4  Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "3     Level 5            Prealgebra  DigitalLearningGmbH/MATH-lighteval   \n", "4     Level 1           Precalculus  DigitalLearningGmbH/MATH-lighteval   \n", "...       ...                   ...                                 ...   \n", "5995  Level 1               Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "5996  Level 2         Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "5997  Level 4  Intermediate Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "5998  Level 4               Algebra  DigitalLearningGmbH/MATH-lighteval   \n", "5999  Level 5         Number Theory  DigitalLearningGmbH/MATH-lighteval   \n", "\n", "                                                 prompt ability  \\\n", "0     [{'content': 'Consider two positive even integ...    math   \n", "1     [{'content': 'Find all $x$ such that $\\lfloor ...    math   \n", "2     [{'content': 'Real numbers $a$ and $b$ are cho...    math   \n", "3     [{'content': 'How many square units are in the...    math   \n", "4     [{'content': 'Find $\\cot 45^\\circ.$ Let's thin...    math   \n", "...                                                 ...     ...   \n", "5995  [{'content': 'Evaluate $(x-a+3)$ if the value ...    math   \n", "5996  [{'content': 'Every bag of Dummies (a chocolat...    math   \n", "5997  [{'content': 'For a positive integer $n,$ let\n", "...    math   \n", "5998  [{'content': 'What is the distance between the...    math   \n", "5999  [{'content': 'Use each of the five digits $2, ...    math   \n", "\n", "                                           reward_model  \\\n", "0               {'ground_truth': '27', 'style': 'rule'}   \n", "1     {'ground_truth': '\\left[ \\frac{5}{2}, \\frac{7}...   \n", "2     {'ground_truth': '\\frac{3 + \\sqrt{5}}{2}', 'st...   \n", "3              {'ground_truth': '714', 'style': 'rule'}   \n", "4                {'ground_truth': '1', 'style': 'rule'}   \n", "...                                                 ...   \n", "5995            {'ground_truth': '10', 'style': 'rule'}   \n", "5996             {'ground_truth': '3', 'style': 'rule'}   \n", "5997             {'ground_truth': '1', 'style': 'rule'}   \n", "5998            {'ground_truth': '13', 'style': 'rule'}   \n", "5999           {'ground_truth': '762', 'style': 'rule'}   \n", "\n", "                             extra_info         cluster  \n", "0     {'index': 1743, 'split': 'train'}  math_cluster_8  \n", "1     {'index': 4475, 'split': 'train'}  math_cluster_6  \n", "2     {'index': 4228, 'split': 'train'}  math_cluster_0  \n", "3     {'index': 6335, 'split': 'train'}  math_cluster_2  \n", "4     {'index': 7409, 'split': 'train'}  math_cluster_1  \n", "...                                 ...             ...  \n", "5995   {'index': 905, 'split': 'train'}  math_cluster_9  \n", "5996  {'index': 5192, 'split': 'train'}  math_cluster_8  \n", "5997  {'index': 3980, 'split': 'train'}  math_cluster_7  \n", "5998   {'index': 235, 'split': 'train'}  math_cluster_4  \n", "5999  {'index': 5157, 'split': 'train'}  math_cluster_8  \n", "\n", "[6000 rows x 8 columns]>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_parquet(FILE_FOLDER)\n", "df.head"]}, {"cell_type": "code", "execution_count": 8, "id": "438dd3ff", "metadata": {}, "outputs": [], "source": ["def eda_parquet(file_path, column_name):\n", "    \"\"\"\n", "    读取Parquet文件并对指定的列进行EDA分析，同时绘制数据分布图。\n", "    \n", "    参数:\n", "        file_path (str): Parquet文件的路径。\n", "        column_name (str): 需要进行EDA分析的列名。\n", "    \n", "    返回:\n", "        dict: 包含EDA分析结果的字典。\n", "    \"\"\"\n", "    # 读取Parquet文件\n", "    try:\n", "        df = pd.read_parquet(file_path)\n", "    except Exception as e:\n", "        return {\"error\": f\"无法读取文件: {e}\"}\n", "    \n", "    # 检查列是否存在\n", "    if column_name not in df.columns:\n", "        return {\"error\": f\"列 '{column_name}' 不存在于数据中\"}\n", "    \n", "    # 提取指定列\n", "    column_data = df[column_name]\n", "    \n", "    # 生成EDA分析结果\n", "    eda_results = {\n", "        \"column_name\": column_name,\n", "        \"data_type\": column_data.dtype,\n", "        \"num_missing\": column_data.isnull().sum(),\n", "        \"num_unique\": column_data.nunique(),\n", "        \"unique_values\": column_data.unique()[:10].tolist(),  # 前10个唯一值\n", "        \"mean\": column_data.mean() if pd.api.types.is_numeric_dtype(column_data) else None,\n", "        \"median\": column_data.median() if pd.api.types.is_numeric_dtype(column_data) else None,\n", "        \"std_dev\": column_data.std() if pd.api.types.is_numeric_dtype(column_data) else None,\n", "        \"min\": column_data.min() if pd.api.types.is_numeric_dtype(column_data) else None,\n", "        \"max\": column_data.max() if pd.api.types.is_numeric_dtype(column_data) else None,\n", "        \"value_counts\": column_data.value_counts().head(10).to_dict()  # 前10个值计数\n", "    }\n", "    \n", "    # 绘制数据分布图\n", "    plt.figure(figsize=(10, 6))\n", "    if pd.api.types.is_numeric_dtype(column_data) or column_data.str.split(\"_\").str[-1].str.isdigit().all():\n", "        numeric_data = column_data.str.split(\"_\").str[-1].astype(float)\n", "        sns.histplot(numeric_data.dropna(), kde=True, bins=10)\n", "        plt.title(f\"Distribution of numeric column derived from '{column_name}'\")\n", "        plt.xlabel(f\"{column_name} (numeric part)\")\n", "    else:\n", "        sns.countplot(y=column_data, order=column_data.value_counts().iloc[:10].index)\n", "        plt.title(f\"Distribution of categorical column '{column_name}' (Top 10 categories)\")\n", "        plt.xlabel(column_name)\n", "    plt.ylabel(\"Frequency\")\n", "    plt.tight_layout()\n", "    plt.show()\n", "    return eda_results"]}, {"cell_type": "code", "execution_count": 9, "id": "be7408af", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["{'column_name': 'cluster',\n", " 'data_type': dtype('O'),\n", " 'num_missing': 0,\n", " 'num_unique': 10,\n", " 'unique_values': ['math_cluster_8',\n", "  'math_cluster_6',\n", "  'math_cluster_0',\n", "  'math_cluster_2',\n", "  'math_cluster_1',\n", "  'math_cluster_9',\n", "  'math_cluster_3',\n", "  'math_cluster_4',\n", "  'math_cluster_5',\n", "  'math_cluster_7'],\n", " 'mean': None,\n", " 'median': None,\n", " 'std_dev': None,\n", " 'min': None,\n", " 'max': None,\n", " 'value_counts': {'math_cluster_0': 894,\n", "  'math_cluster_4': 765,\n", "  'math_cluster_3': 654,\n", "  'math_cluster_8': 628,\n", "  'math_cluster_6': 600,\n", "  'math_cluster_7': 589,\n", "  'math_cluster_2': 535,\n", "  'math_cluster_5': 516,\n", "  'math_cluster_1': 468,\n", "  'math_cluster_9': 351}}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["eda_parquet(FILE_FOLDER, \"cluster\")"]}, {"cell_type": "code", "execution_count": 10, "id": "b382e6a6", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'error': \"列 'difficulty_bin' 不存在于数据中\"}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["eda_parquet(FILE_FOLDER, \"difficulty_bin\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}