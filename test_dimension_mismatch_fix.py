#!/usr/bin/env python3
"""
Test script to reproduce and fix the specific dimension mismatch errors.
"""

import torch
import torch.nn as nn
import logging

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_problematic_dimensions():
    """Test with the exact dimensions that are causing the errors."""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Testing on device: {device}")
    
    # Create a model that reproduces the problematic dimensions
    # Based on the error: input shape [1, 1024, 1536] but expecting different dimensions
    class ProblematicModel(nn.Module):
        def __init__(self):
            super().__init__()
            # These dimensions are designed to trigger the reported errors
            self.layer1 = nn.Linear(1536, 2048)  # Input: 1536, might get reshaped incorrectly
            self.layer2 = nn.Linear(2048, 1024)  # This might cause the 2048 vs 1024 mismatch
            self.layer3 = nn.Linear(1024, 4096)  # This might cause the 8192 vs 4096 mismatch
            
        def forward(self, input_ids, attention_mask=None, **kwargs):
            # Simulate the problematic tensor flow
            # Input: [1, 1024, 1536] as reported in debug info
            x = torch.randn(input_ids.shape[0], 1024, 1536, device=input_ids.device)
            
            # Apply transformations that might cause dimension issues
            x = self.layer1(x)  # [1, 1024, 1536] -> [1, 1024, 2048]
            x = torch.relu(x)
            x = self.layer2(x)  # [1, 1024, 2048] -> [1, 1024, 1024]
            x = torch.relu(x)
            logits = self.layer3(x)  # [1, 1024, 1024] -> [1, 1024, 4096]
            
            return type('Output', (), {'logits': logits})()
    
    model = ProblematicModel().to(device)
    
    print("Model architecture:")
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear):
            print(f"  {name}: {module.in_features} -> {module.out_features}")
    
    # Create test data that matches the error report
    batch = {
        'input_ids': torch.randint(0, 1000, (1, 512)).to(device),  # Smaller for testing
        'attention_mask': torch.ones(1, 512).to(device)
    }
    
    def loss_fn(model_output, batch_data):
        logits = model_output.logits  # [1, 1024, 4096]
        # Create labels that match the output shape
        labels = torch.randint(0, 4096, (logits.shape[0], logits.shape[1])).to(logits.device)
        return torch.nn.functional.cross_entropy(
            logits.view(-1, logits.size(-1)),
            labels.view(-1)
        )
    
    print("\n=== Testing K-FAC with Problematic Dimensions ===")
    
    try:
        from verl.utils.influence_functions import KFACInfluenceFunctionCalculator
        
        calculator = KFACInfluenceFunctionCalculator(
            model=model,
            regularization_lambda=1e-3,
            kfac_damping=1e-3,
            use_kfac=True
        )
        
        print("Testing K-FAC factor extraction...")
        calculator.extract_kfac_factors(batch, loss_fn)
        
        if calculator.kfac_factors:
            print(f"✅ Successfully extracted {len(calculator.kfac_factors)} K-FAC factors:")
            for key, factor in calculator.kfac_factors.items():
                print(f"  {key}: {factor.shape}")
        else:
            print("❌ No K-FAC factors extracted")
            return False
        
        # Test influence score computation
        print("\nTesting influence score computation...")
        calculator.compute_validation_gradients(batch, loss_fn)
        
        training_sample = {
            'input_ids': torch.randint(0, 1000, (1, 512)).to(device),
            'attention_mask': torch.ones(1, 512).to(device)
        }
        
        influence_score = calculator.compute_influence_score(
            training_sample, batch, loss_fn
        )
        
        print(f"✅ Influence score computed: {influence_score:.6f}")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_case_dimensions():
    """Test various edge cases that might cause dimension mismatches."""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\n=== Testing Edge Case Dimensions ===")
    
    edge_cases = [
        {
            'name': 'Transposed dimensions',
            'input_shape': (1, 1536, 1024),  # Swapped seq_len and hidden_dim
            'linear_in': 1024,
            'linear_out': 2048
        },
        {
            'name': 'Multiple heads',
            'input_shape': (1, 8, 128, 64),  # [batch, heads, seq_len, head_dim]
            'linear_in': 64,
            'linear_out': 512
        },
        {
            'name': 'Irregular dimensions',
            'input_shape': (2, 333, 777),  # Odd dimensions
            'linear_in': 777,
            'linear_out': 555
        }
    ]
    
    for case in edge_cases:
        print(f"\nTesting {case['name']}...")
        
        class EdgeCaseModel(nn.Module):
            def __init__(self, in_features, out_features):
                super().__init__()
                self.linear = nn.Linear(in_features, out_features)
                
            def forward(self, input_ids, **kwargs):
                # Create tensor with specific problematic shape
                x = torch.randn(case['input_shape'], device=input_ids.device)
                logits = self.linear(x)
                return type('Output', (), {'logits': logits})()
        
        model = EdgeCaseModel(case['linear_in'], case['linear_out']).to(device)
        
        batch = {
            'input_ids': torch.randint(0, 100, (1, 32)).to(device),
            'attention_mask': torch.ones(1, 32).to(device)
        }
        
        def loss_fn(model_output, batch_data):
            logits = model_output.logits
            # Create compatible labels
            target_shape = logits.shape[:-1]
            labels = torch.randint(0, case['linear_out'], target_shape).to(logits.device)
            return torch.nn.functional.cross_entropy(
                logits.view(-1, logits.size(-1)),
                labels.view(-1)
            )
        
        try:
            from verl.utils.influence_functions import KFACInfluenceFunctionCalculator
            
            calculator = KFACInfluenceFunctionCalculator(
                model=model,
                regularization_lambda=1e-3,
                kfac_damping=1e-3,
                use_kfac=True
            )
            
            calculator.extract_kfac_factors(batch, loss_fn)
            
            if calculator.kfac_factors:
                print(f"  ✅ {case['name']}: Successfully extracted factors")
                for key, factor in calculator.kfac_factors.items():
                    print(f"    {key}: {factor.shape}")
            else:
                print(f"  ⚠️ {case['name']}: No factors extracted, but no crash")
                
        except Exception as e:
            print(f"  ❌ {case['name']}: Failed with error: {e}")
            return False
    
    return True

if __name__ == "__main__":
    print("🧪 Testing dimension mismatch fixes...")
    
    success1 = test_problematic_dimensions()
    success2 = test_edge_case_dimensions()
    
    if success1 and success2:
        print("\n🎉 All dimension mismatch tests passed!")
    else:
        print("\n💥 Some dimension mismatch tests failed.")
