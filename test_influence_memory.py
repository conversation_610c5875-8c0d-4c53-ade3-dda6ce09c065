#!/usr/bin/env python3
"""
Test script to verify memory-efficient influence function computation.
"""

import torch
import torch.nn as nn
from typing import Dict
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_memory_efficient_influence():
    """Test the memory-efficient influence function implementation."""
    
    # Create a simple model for testing
    class SimpleModel(nn.Module):
        def __init__(self, vocab_size=1000, hidden_size=128):
            super().__init__()
            self.embedding = nn.Embedding(vocab_size, hidden_size)
            self.linear1 = nn.Linear(hidden_size, hidden_size)
            self.linear2 = nn.Linear(hidden_size, vocab_size)
            
        def forward(self, input_ids, attention_mask=None, **kwargs):
            x = self.embedding(input_ids)
            x = torch.mean(x, dim=1)  # Simple pooling
            x = torch.relu(self.linear1(x))
            logits = self.linear2(x)
            return type('Output', (), {'logits': logits})()
    
    # Initialize model and move to GPU if available
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = SimpleModel().to(device)
    
    print(f"Testing on device: {device}")
    
    # Create test data
    batch_size = 4
    seq_len = 32
    
    validation_batch = {
        'input_ids': torch.randint(0, 1000, (batch_size, seq_len)).to(device),
        'attention_mask': torch.ones(batch_size, seq_len).to(device)
    }
    
    training_sample = {
        'input_ids': torch.randint(0, 1000, (1, seq_len)).to(device),
        'attention_mask': torch.ones(1, seq_len).to(device)
    }
    
    # Define loss function
    def loss_fn(model_output, batch_data):
        logits = model_output.logits  # Shape: [batch_size, vocab_size]
        # Create simple labels for testing (use first token id as target)
        labels = batch_data['input_ids'][:, 0]  # Shape: [batch_size]
        return torch.nn.functional.cross_entropy(logits, labels)
    
    # Test the influence function calculator
    try:
        from verl.utils.influence_functions import create_influence_calculator
        
        config = {
            'use_kfac': True,
            'regularization_lambda': 1e-3,
            'damping_factor': 1e-3,
            'kfac_damping': 1e-3,
            'max_samples_per_batch': 1
        }
        
        calculator = create_influence_calculator(model=model, config=config)
        
        print("Computing validation gradients...")
        calculator.compute_validation_gradients(validation_batch, loss_fn)
        
        print("Computing influence score...")
        influence_score = calculator.compute_influence_score(
            training_sample, validation_batch, loss_fn
        )
        
        print(f"Influence score computed successfully: {influence_score}")
        
        # Test memory usage
        if torch.cuda.is_available():
            memory_allocated = torch.cuda.memory_allocated() / 1024**3  # GB
            memory_reserved = torch.cuda.memory_reserved() / 1024**3   # GB
            print(f"GPU Memory - Allocated: {memory_allocated:.2f} GB, Reserved: {memory_reserved:.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"Error in influence function computation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_memory_efficient_influence()
    if success:
        print("✅ Memory-efficient influence function test passed!")
    else:
        print("❌ Memory-efficient influence function test failed!")
