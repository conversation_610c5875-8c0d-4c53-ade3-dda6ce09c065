import subprocess
import sys
import os
import threading

os.environ["WANDB_MODE"] = "offline"

def read_stream(stream, prefix):
    """读取流并逐行打印"""
    for line in iter(stream.readline, ''):
        print(f"{prefix}: {line.strip()}")

def run_script(script_path):
    try:
        process = subprocess.Popen(
            [script_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # 创建两个线程分别读取 stdout 和 stderr
        stdout_thread = threading.Thread(target=read_stream, args=(process.stdout, "STDOUT"), daemon=True)
        stderr_thread = threading.Thread(target=read_stream, args=(process.stderr, "STDERR"), daemon=True)

        stdout_thread.start()
        stderr_thread.start()

        # 等待子进程结束
        process.wait()

        # 等待线程结束（可选）
        stdout_thread.join(timeout=1)
        stderr_thread.join(timeout=1)

        if process.returncode != 0:
            print(f"脚本执行失败（退出码 {process.returncode}）")
            sys.exit(1)
        else:
            print("脚本执行成功")

    except FileNotFoundError:
        print(f"错误：脚本文件 '{script_path}' 未找到。")
        sys.exit(1)
    except Exception as e:
        print(f"发生未知错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="执行指定的脚本")
    parser.add_argument("script_path", type=str, help="要执行的脚本路径")
    args = parser.parse_args()
    script_path = args.script_path
    print(f"Current working directory: {os.getcwd()}" + "🌴🌴🌴🌴"* 10)
    # 列举当前目录文件
    for filename in os.listdir():
        print(filename)
    run_script(script_path)
    print("训练完成，开始压缩wandb、local_save_dir、output文件夹为tar.gz文件")
    # 将wandb outputs local_save_dir 这3个目录分别压缩，压缩包名称包含当前时间
    import datetime
    now = str(datetime.datetime.now()).replace(" ","-")
    subprocess.run(["tar", "-czvf", f"/data/oss_bucket_0/Users/<USER>/output/wandb-{now}.tar.gz", "wandb"])
    # subprocess.run(["tar", "-czvf", f"/data/oss_bucket_0/Users/<USER>/output/local_save_dir-{now}.tar.gz", "local_save_dir"])
    # subprocess.run(["tar", "-czvf", f"/data/oss_bucket_0/Users/<USER>/output/output-{now}.tar.gz", "output"])
    print("压缩完成")