"""
Tests for influence function implementation in curriculum learning.
"""

import pytest
import torch
import torch.nn as nn
import numpy as np
from unittest.mock import Mock, patch

from verl.utils.influence_functions import (
    InfluenceFunctionCalculator, 
    KFACInfluenceFunctionCalculator,
    create_influence_calculator
)
from verl.utils.kfac import <PERSON><PERSON><PERSON><PERSON><PERSON>, KFACOptimizer
from verl.utils.curriculum_learning import LearnabilityEstimator, CurriculumSampler


class SimpleModel(nn.Module):
    """Simple model for testing."""
    def __init__(self, input_size=10, hidden_size=5, output_size=2):
        super().__init__()
        self.linear1 = nn.Linear(input_size, hidden_size)
        self.linear2 = nn.Linear(hidden_size, output_size)
        
    def forward(self, x):
        x = torch.relu(self.linear1(x))
        return self.linear2(x)


@pytest.fixture
def simple_model():
    """Create a simple model for testing."""
    return SimpleModel()


@pytest.fixture
def sample_batch():
    """Create a sample batch for testing."""
    batch_size = 4
    seq_len = 8
    vocab_size = 100
    
    return {
        'input_ids': torch.randint(0, vocab_size, (batch_size, seq_len)),
        'attention_mask': torch.ones(batch_size, seq_len),
        'responses': torch.randint(0, vocab_size, (batch_size, seq_len // 2))
    }


@pytest.fixture
def simple_loss_fn():
    """Create a simple loss function for testing."""
    def loss_fn(outputs, batch):
        # Simple MSE loss for testing
        target = torch.randn_like(outputs)
        return torch.nn.functional.mse_loss(outputs, target)
    return loss_fn


class TestInfluenceFunctionCalculator:
    """Test basic influence function calculator."""
    
    def test_initialization(self, simple_model):
        """Test calculator initialization."""
        calculator = InfluenceFunctionCalculator(
            model=simple_model,
            regularization_lambda=1e-3,
            damping_factor=1e-3,
            use_kfac=False
        )
        
        assert calculator.model == simple_model
        assert calculator.regularization_lambda == 1e-3
        assert calculator.damping_factor == 1e-3
        assert not calculator.use_kfac
        assert calculator.validation_gradients is None
    
    def test_compute_gradients(self, simple_model, simple_loss_fn):
        """Test gradient computation."""
        calculator = InfluenceFunctionCalculator(simple_model)
        
        # Create simple input
        batch = {'input': torch.randn(2, 10)}
        
        # Mock the model forward pass
        with patch.object(simple_model, 'forward') as mock_forward:
            mock_forward.return_value = torch.randn(2, 2)
            
            gradients = calculator.compute_gradients(batch, simple_loss_fn)
            
            # Check that gradients were computed
            assert isinstance(gradients, dict)
            assert len(gradients) > 0
            
            # Check that all parameters have gradients
            for name, param in simple_model.named_parameters():
                if param.requires_grad:
                    assert name in gradients
                    assert gradients[name].shape == param.shape
    
    def test_flatten_gradients(self, simple_model):
        """Test gradient flattening."""
        calculator = InfluenceFunctionCalculator(simple_model)
        
        # Create mock gradients
        gradients = {
            'linear1.weight': torch.randn(5, 10),
            'linear1.bias': torch.randn(5),
            'linear2.weight': torch.randn(2, 5),
            'linear2.bias': torch.randn(2)
        }
        
        flat_grad = calculator.flatten_gradients(gradients)
        
        # Check shape
        expected_size = 5*10 + 5 + 2*5 + 2  # 50 + 5 + 10 + 2 = 67
        assert flat_grad.shape == (expected_size,)
        
        # Check that it's a proper flattening
        assert torch.is_tensor(flat_grad)
    
    def test_validation_gradients_caching(self, simple_model, simple_loss_fn):
        """Test validation gradient caching."""
        calculator = InfluenceFunctionCalculator(simple_model)
        
        batch = {'input': torch.randn(2, 10)}
        
        with patch.object(calculator, 'compute_gradients') as mock_compute:
            mock_gradients = {'param1': torch.randn(5, 5)}
            mock_compute.return_value = mock_gradients
            
            # First call should compute gradients
            calculator.compute_validation_gradients(batch, simple_loss_fn)
            assert calculator.validation_gradients == mock_gradients
            mock_compute.assert_called_once()


class TestKFACLayer:
    """Test K-FAC layer implementation."""
    
    def test_initialization(self):
        """Test K-FAC layer initialization."""
        layer = nn.Linear(10, 5)
        kfac_layer = KFACLayer(layer, damping=1e-3)
        
        assert kfac_layer.layer == layer
        assert kfac_layer.damping == 1e-3
        assert kfac_layer.A is None
        assert kfac_layer.G is None
        assert kfac_layer.num_updates == 0
    
    def test_update_A(self):
        """Test input covariance matrix update."""
        layer = nn.Linear(10, 5, bias=True)
        kfac_layer = KFACLayer(layer)
        
        # Test input update
        input_tensor = torch.randn(4, 10)  # batch_size=4, input_size=10
        kfac_layer.update_A(input_tensor)
        
        # A should be (input_size + 1) x (input_size + 1) due to bias
        assert kfac_layer.A.shape == (11, 11)  # 10 + 1 for bias
        assert torch.is_tensor(kfac_layer.A)
    
    def test_update_G(self):
        """Test output gradient covariance matrix update."""
        layer = nn.Linear(10, 5)
        kfac_layer = KFACLayer(layer)
        
        # Test gradient update
        grad_output = torch.randn(4, 5)  # batch_size=4, output_size=5
        kfac_layer.update_G(grad_output)
        
        # G should be output_size x output_size
        assert kfac_layer.G.shape == (5, 5)
        assert torch.is_tensor(kfac_layer.G)
    
    def test_get_inverse_factors(self):
        """Test K-FAC factor inversion."""
        layer = nn.Linear(3, 2, bias=False)  # Small sizes for testing
        kfac_layer = KFACLayer(layer, damping=1e-2)
        
        # Set up some test matrices
        kfac_layer.A = torch.eye(3) + 0.1 * torch.randn(3, 3)
        kfac_layer.G = torch.eye(2) + 0.1 * torch.randn(2, 2)
        
        A_inv, G_inv = kfac_layer.get_inverse_factors()
        
        assert A_inv.shape == (3, 3)
        assert G_inv.shape == (2, 2)
        
        # Test that they are actually inverses (approximately)
        A_damped = kfac_layer.A + kfac_layer.damping * torch.eye(3)
        G_damped = kfac_layer.G + kfac_layer.damping * torch.eye(2)
        
        assert torch.allclose(torch.mm(A_inv, A_damped), torch.eye(3), atol=1e-4)
        assert torch.allclose(torch.mm(G_inv, G_damped), torch.eye(2), atol=1e-4)


class TestKFACInfluenceFunctionCalculator:
    """Test K-FAC influence function calculator."""
    
    def test_initialization(self, simple_model):
        """Test K-FAC calculator initialization."""
        calculator = KFACInfluenceFunctionCalculator(
            model=simple_model,
            use_kfac=True,
            kfac_damping=1e-3
        )
        
        assert calculator.use_kfac
        assert calculator.kfac_damping == 1e-3
        assert calculator.kfac_factors == {}
    
    def test_factory_function(self, simple_model):
        """Test factory function for creating calculators."""
        # Test K-FAC calculator creation
        config = {
            'use_kfac': True,
            'regularization_lambda': 1e-3,
            'kfac_damping': 1e-3
        }
        
        calculator = create_influence_calculator(simple_model, config)
        assert isinstance(calculator, KFACInfluenceFunctionCalculator)
        assert calculator.use_kfac
        
        # Test standard calculator creation
        config['use_kfac'] = False
        calculator = create_influence_calculator(simple_model, config)
        assert isinstance(calculator, InfluenceFunctionCalculator)
        assert not calculator.use_kfac


class TestCurriculumLearningIntegration:
    """Test integration with curriculum learning system."""
    
    def test_learnability_estimator_influence_scores(self):
        """Test LearnabilityEstimator with influence scores."""
        estimator = LearnabilityEstimator()
        
        # Test without influence functions
        assert not estimator.use_influence_functions
        assert estimator.recent_influence_scores == []
        
        # Enable influence functions
        estimator.use_influence_functions = True
        
        # Update with influence scores
        rewards = np.array([1.0, 2.0, 3.0])
        advantages = np.array([0.5, 1.0, 1.5])
        influence_scores = np.array([0.1, -0.2, 0.3])
        
        estimator.update(rewards, advantages, influence_scores=influence_scores)
        
        assert len(estimator.recent_influence_scores) == 3
        assert estimator.mean_influence_score == np.mean(influence_scores)
        
        # Test expected value calculation with influence scores
        expected_value = estimator.expected_value
        assert 0 < expected_value < 1  # Should be normalized to (0, 1)
    
    def test_curriculum_sampler_influence_integration(self):
        """Test CurriculumSampler with influence function support."""
        # Create mock dataset
        mock_dataset = Mock()
        mock_dataset.__len__ = Mock(return_value=10)
        mock_dataset.__getitem__ = Mock(side_effect=lambda i: {'data_source': f'source_{i % 2}'})
        
        sampler = CurriculumSampler(
            dataset=mock_dataset,
            data_source_key='data_source',
            batch_size=2
        )
        
        # Test enabling influence functions
        sampler.enable_influence_functions(True)
        
        # Check that all estimators have influence functions enabled
        for estimator in sampler.curriculum_controller.estimators.values():
            assert estimator.use_influence_functions
        
        # Test update with influence scores
        source_rewards = {'source_0': [1.0, 2.0], 'source_1': [3.0, 4.0]}
        source_advantages = {'source_0': [0.5, 1.0], 'source_1': [1.5, 2.0]}
        source_influence_scores = {'source_0': [0.1, -0.1], 'source_1': [0.2, -0.2]}
        
        sampler.update_weights(
            source_rewards=source_rewards,
            source_advantages=source_advantages,
            source_influence_scores=source_influence_scores
        )
        
        # Check that influence scores were recorded
        stats = sampler.get_source_stats()
        for source in ['source_0', 'source_1']:
            assert 'mean_influence_score' in stats[source]
            assert 'use_influence_functions' in stats[source]
            assert stats[source]['use_influence_functions']


if __name__ == "__main__":
    pytest.main([__file__])
