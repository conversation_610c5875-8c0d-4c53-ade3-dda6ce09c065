
outputs/
wandb/
local_save_dir/
*.png
xdl*
commit*.sh
cluster.json

#####################
# Python specific
*.pyc
__pycache__/
*.pyo
*.pyd
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
*.egg-info/
.installed.cfg
*.egg
pip-log.txt
pip-delete-this-directory.txt
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Django specific
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Flask specific
instance/
.webassets-cache

# Scrapy specific
.scrapy

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# Virtualenv
.python-version
pyvenv.cfg
bin/
include/
lib/
lib64/
share/

# Distribution / packaging
.Python
build/
dist/
downloads/
eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.whl
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs and editors
.idea/
*.iml
.vscode/
*.sublime-project
*.sublime-workspace
.project
.pydevproject

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.swp
*~
