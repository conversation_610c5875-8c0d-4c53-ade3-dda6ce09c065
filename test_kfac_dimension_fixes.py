#!/usr/bin/env python3
"""
Test script to verify K-FAC dimension fixes with detailed debugging.
"""

import torch
import torch.nn as nn
import logging
from typing import Dict

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_kfac_with_realistic_transformer():
    """Test K-FAC with a realistic transformer-like model that mimics production architectures."""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Testing on device: {device}")
    
    # Create a model that mimics real transformer architectures
    class RealisticTransformer(nn.Module):
        def __init__(self, vocab_size=32000, hidden_size=2048, intermediate_size=8192, seq_len=512):
            super().__init__()
            self.embedding = nn.Embedding(vocab_size, hidden_size)
            
            # Multi-layer transformer-like structure
            self.attention_proj = nn.Linear(hidden_size, hidden_size)  # 2048 -> 2048
            self.attention_out = nn.Linear(hidden_size, hidden_size)   # 2048 -> 2048
            
            # Feed-forward layers (typical transformer FFN)
            self.ffn_up = nn.Linear(hidden_size, intermediate_size)     # 2048 -> 8192
            self.ffn_down = nn.Linear(intermediate_size, hidden_size)   # 8192 -> 2048
            
            # Output projection
            self.output_proj = nn.Linear(hidden_size, vocab_size)       # 2048 -> 32000
            
        def forward(self, input_ids, attention_mask=None, **kwargs):
            # Shape: [batch_size, seq_len, hidden_size]
            x = self.embedding(input_ids)
            
            # Attention-like operations
            attn = self.attention_proj(x)
            attn = torch.relu(attn)
            x = x + self.attention_out(attn)
            
            # Feed-forward network
            ffn = self.ffn_up(x)
            ffn = torch.relu(ffn)
            ffn = self.ffn_down(ffn)
            x = x + ffn
            
            # Output projection
            logits = self.output_proj(x)  # [batch_size, seq_len, vocab_size]
            
            return type('Output', (), {'logits': logits})()
    
    # Test with realistic dimensions that cause the reported errors
    model = RealisticTransformer().to(device)
    
    print(f"Model architecture:")
    for name, module in model.named_modules():
        if isinstance(module, nn.Linear):
            print(f"  {name}: {module.in_features} -> {module.out_features}")
    
    # Create test data with dimensions that trigger the errors
    test_configs = [
        {
            'name': 'Small batch',
            'batch_size': 1,
            'seq_len': 128,
        },
        {
            'name': 'Medium batch', 
            'batch_size': 2,
            'seq_len': 256,
        }
    ]
    
    for config in test_configs:
        print(f"\n=== Testing {config['name']} ===")
        
        # Create test data
        validation_batch = {
            'input_ids': torch.randint(0, 1000, (config['batch_size'], config['seq_len'])).to(device),
            'attention_mask': torch.ones(config['batch_size'], config['seq_len']).to(device)
        }
        
        training_sample = {
            'input_ids': torch.randint(0, 1000, (1, config['seq_len'])).to(device),
            'attention_mask': torch.ones(1, config['seq_len']).to(device)
        }
        
        print(f"Validation batch shapes:")
        for key, value in validation_batch.items():
            print(f"  {key}: {value.shape}")
        
        # Define loss function
        def loss_fn(model_output, batch_data):
            logits = model_output.logits
            labels = batch_data['input_ids']
            logits_flat = logits.view(-1, logits.size(-1))
            labels_flat = labels.view(-1)
            return torch.nn.functional.cross_entropy(logits_flat, labels_flat, ignore_index=-100)
        
        # Test K-FAC factor extraction
        try:
            from verl.utils.influence_functions import KFACInfluenceFunctionCalculator
            
            calculator = KFACInfluenceFunctionCalculator(
                model=model,
                regularization_lambda=1e-3,
                kfac_damping=1e-3,
                use_kfac=True
            )
            
            print("Testing K-FAC factor extraction...")
            calculator.extract_kfac_factors(validation_batch, loss_fn)
            
            print(f"Extracted factors:")
            for key, factor in calculator.kfac_factors.items():
                print(f"  {key}: {factor.shape}")
            
            # Test influence score computation
            print("Testing influence score computation...")
            calculator.compute_validation_gradients(validation_batch, loss_fn)
            
            influence_score = calculator.compute_influence_score(
                training_sample, validation_batch, loss_fn
            )
            
            print(f"✅ {config['name']}: Influence score = {influence_score:.6f}")
            
            # Check memory usage
            if torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated() / 1024**2  # MB
                print(f"Memory allocated: {memory_allocated:.1f} MB")
                
        except Exception as e:
            print(f"❌ {config['name']}: Failed with error: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

def test_dimension_edge_cases():
    """Test edge cases that commonly cause dimension mismatches."""
    print("\n=== Testing Dimension Edge Cases ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Test different tensor shapes that commonly cause issues
    edge_cases = [
        {
            'name': 'Very wide layer (8192->4096)',
            'in_features': 8192,
            'out_features': 4096,
            'batch_size': 1,
            'seq_len': 64
        },
        {
            'name': 'Very narrow layer (512->128)', 
            'in_features': 512,
            'out_features': 128,
            'batch_size': 2,
            'seq_len': 32
        },
        {
            'name': 'Asymmetric layer (2048->1024)',
            'in_features': 2048, 
            'out_features': 1024,
            'batch_size': 1,
            'seq_len': 128
        }
    ]
    
    for case in edge_cases:
        print(f"\nTesting {case['name']}...")
        
        # Create simple model with specific dimensions
        class EdgeCaseModel(nn.Module):
            def __init__(self, in_features, out_features):
                super().__init__()
                self.linear = nn.Linear(in_features, out_features)
                
            def forward(self, input_ids, **kwargs):
                # Simulate embedding lookup
                x = torch.randn(input_ids.shape[0], input_ids.shape[1], self.linear.in_features, 
                              device=input_ids.device, dtype=torch.float32)
                logits = self.linear(x)
                return type('Output', (), {'logits': logits})()
        
        model = EdgeCaseModel(case['in_features'], case['out_features']).to(device)
        
        # Create test data
        batch = {
            'input_ids': torch.randint(0, 100, (case['batch_size'], case['seq_len'])).to(device),
            'attention_mask': torch.ones(case['batch_size'], case['seq_len']).to(device)
        }
        
        def loss_fn(model_output, batch_data):
            logits = model_output.logits
            labels = torch.randint(0, case['out_features'], logits.shape[:2]).to(logits.device)
            return torch.nn.functional.cross_entropy(
                logits.view(-1, logits.size(-1)),
                labels.view(-1)
            )
        
        try:
            from verl.utils.influence_functions import KFACInfluenceFunctionCalculator
            
            calculator = KFACInfluenceFunctionCalculator(
                model=model,
                regularization_lambda=1e-3,
                kfac_damping=1e-3,
                use_kfac=True
            )
            
            calculator.extract_kfac_factors(batch, loss_fn)
            
            print(f"  ✅ {case['name']}: K-FAC factors extracted successfully")
            for key, factor in calculator.kfac_factors.items():
                print(f"    {key}: {factor.shape}")
                
        except Exception as e:
            print(f"  ❌ {case['name']}: Failed with error: {e}")
            return False
    
    return True

if __name__ == "__main__":
    print("🧪 Running comprehensive K-FAC dimension fix tests...")
    
    success1 = test_kfac_with_realistic_transformer()
    success2 = test_dimension_edge_cases()
    
    if success1 and success2:
        print("\n🎉 All K-FAC dimension tests passed!")
    else:
        print("\n💥 Some K-FAC dimension tests failed.")
